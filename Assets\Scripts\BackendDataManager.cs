/**
 * BackendDataManager.cs
 * 
 * 负责管理与后端（或本地文件系统模拟）的数据交互。
 * 包括保存任务完成照片以及加载任务数据。
 * 使用单例模式提供全局访问点。
 */
using UnityEngine;
using System.IO;
using System.Collections.Generic; // 为List添加引用

public class BackendDataManager : MonoBehaviour
{
    // --- Singleton Pattern ---
    private static BackendDataManager _instance;
    public static BackendDataManager Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = FindObjectOfType<BackendDataManager>();
                if (_instance == null)
                {
                    GameObject singletonObject = new GameObject("BackendDataManager");
                    _instance = singletonObject.AddComponent<BackendDataManager>();
                    DontDestroyOnLoad(singletonObject); 
                    Debug.Log("BackendDataManager instance created.");
                }
            }
            return _instance;
        }
    }

    // 事件：当任务数据更新时触发
    public delegate void TaskDataUpdatedHandler();
    public event TaskDataUpdatedHandler OnTaskDataUpdated;

    // 用于解析TaskData的Json数据结构
    [System.Serializable]
    private class TaskListDataHelper
    {
        public List<ARTaskUIManager.TaskData> data;
    }

    // --- Paths ---
    [Header("存储路径设置")]
    [SerializeField] private string taskPhotoFolderName = "Datas";
    [SerializeField] private string taskDataFileName = "Tasks.json";

    private string taskPhotoPath;
    private string taskDataFilePath;

    // 任务数据缓存
    private List<ARTaskUIManager.TaskData> _cachedTaskData = null;
    private bool _isDataDirty = true;

    // --- Initialization ---
    private void Awake()
    {
        if (_instance != null && _instance != this)
        {
            Destroy(gameObject);
            return;
        }
        _instance = this;
        DontDestroyOnLoad(gameObject);

        InitializePaths();
    }

    private void Start()
    {
        // 确保在启动时通知所有监听器
        NotifyTaskDataUpdated();
    }

    private void InitializePaths()
    {
        taskPhotoPath = Path.Combine(Application.persistentDataPath, taskPhotoFolderName);
        taskDataFilePath = Path.Combine(Application.persistentDataPath, taskDataFileName);

        // Create directories if they don't exist
        if (!Directory.Exists(taskPhotoPath))
        {
            Directory.CreateDirectory(taskPhotoPath);
            Debug.Log($"Created directory: {taskPhotoPath}");
        }
        // Ensure the directory for the JSON file exists
        string taskDataDirectory = Path.GetDirectoryName(taskDataFilePath);
        if (!Directory.Exists(taskDataDirectory))
        {
            Directory.CreateDirectory(taskDataDirectory);
            Debug.Log($"Created directory: {taskDataDirectory}");
        }
    }
    

    // --- Public Methods ---

    /// <summary>
    /// 保存第二次自动拍摄的任务完成照片。
    /// </summary>
    /// <param name="photoTexture">照片纹理</param>
    /// <param name="fileName">要保存的文件名 (包含扩展名, 如 1_5.png)</param>
    /// <returns>如果保存成功返回true，否则返回false</returns>
    public bool SaveTaskCompletionPhoto(Texture2D photoTexture, string fileName)
    {
        if (photoTexture == null || string.IsNullOrEmpty(fileName))
        {
            Debug.LogError("SaveTaskCompletionPhoto: 照片纹理或文件名为空。");
            return false;
        }

        try
        {
            byte[] bytes = photoTexture.EncodeToPNG();
            string fullPath = Path.Combine(taskPhotoPath, fileName);
            File.WriteAllBytes(fullPath, bytes);
            Debug.Log($"任务完成照片已保存至: {fullPath}");
            return true;
        }
        catch (System.Exception e)
        {
            Debug.LogError($"保存任务完成照片失败: {e.Message}");
            return false;
        }
    }

    /// <summary>
    /// 从指定路径加载任务数据的JSON字符串。
    /// (未来可以扩展为从网络加载)
    /// </summary>
    /// <returns>包含任务数据的JSON字符串，如果加载失败则返回null</returns>
    public string LoadTaskDataJson()
    {
        if (!File.Exists(taskDataFilePath))
        {
            Debug.LogError($"任务数据文件未找到: {taskDataFilePath}");
        }

        try
        {
            string json = File.ReadAllText(taskDataFilePath);
            Debug.Log($"任务数据已从 {taskDataFilePath} 加载。");
            _isDataDirty = true; // 标记数据已更新
            return json;
        }
        catch (System.Exception e)
        {
            Debug.LogError($"加载任务数据失败: {e.Message}");
            return null;
        }
    }

    // BackendDataManager现在直接负责解析JSON
    /// <summary>
    /// 加载并解析任务数据JSON，返回完整的任务数据列表。
    /// </summary>
    /// <returns>任务数据列表，如果加载或解析失败则返回空列表</returns>
    public List<ARTaskUIManager.TaskData> LoadAndParseTaskData()
    {
        if (_cachedTaskData != null && !_isDataDirty)
        {
            return _cachedTaskData;
        }
        
        string json = LoadTaskDataJson();
        if (string.IsNullOrEmpty(json))
        {
            Debug.LogWarning("无法加载任务数据JSON，返回空任务列表。");
            _cachedTaskData = new List<ARTaskUIManager.TaskData>();
            _isDataDirty = false;
            return _cachedTaskData;
        }

        List<ARTaskUIManager.TaskData> parsedTasks = null;
        string processedJson = json.Trim(); // Trim whitespace

        // 检查JSON是否为根级数组格式, e.g., "[{...}, {...}]"
        if (processedJson.StartsWith("[") && processedJson.EndsWith("]"))
        {
            // 将其包装以适应TaskListDataHelper, e.g., "{\"data\": [{...}, {...}]}"
            processedJson = "{ \"data\": " + processedJson + "}";
            Debug.Log("检测到根级JSON数组，已包装处理。");
        }

        try
        {
            // 尝试使用TaskListDataHelper解析 (现在可以处理包装后的数组或原生的{"data":...}格式)
            TaskListDataHelper taskListHelper = JsonUtility.FromJson<TaskListDataHelper>(processedJson);
            if (taskListHelper != null && taskListHelper.data != null)
            {
                parsedTasks = taskListHelper.data;
                Debug.Log($"成功通过 TaskListDataHelper 解析了 {parsedTasks.Count} 个任务。");
            }
            else
            {
                // JsonUtility.FromJson<T>在无法解析时返回null，或者字段不匹配时内部字段为null
                Debug.LogWarning("TaskListDataHelper 解析结果为空或数据字段为空。原始JSON可能不完全匹配预期结构。");
            }
        }
        catch (System.Exception e)
        {
            // 这种捕获通常针对的是JSON语法本身无效的情况
            Debug.LogError($"使用 TaskListDataHelper 解析JSON时发生异常: {e.Message}. 原始JSON: {json.Substring(0, Mathf.Min(json.Length, 200))}(...)");
        }

        if (parsedTasks != null && parsedTasks.Count > 0)
        {
            foreach (var task in parsedTasks)
            {
                // 确保tagData不为null才访问Count，避免NullReferenceException
                Debug.Log($"解析到任务: 编号={task.number}, 位置={task.location}, 内容={task.content}, Tag数量={task.tagData?.Count ?? 0}");
            }
            _cachedTaskData = parsedTasks;
            _isDataDirty = false;
            return _cachedTaskData;
        }
        else
        {
            Debug.LogError("JSON解析最终失败或未包含有效任务数据。返回空列表。");
            _cachedTaskData = new List<ARTaskUIManager.TaskData>();
            _isDataDirty = false;
            return _cachedTaskData;
        }
    }
    /// <summary>
    /// 保存更新的任务数据到JSON文件
    /// </summary>
    /// <param name="taskData">任务数据列表</param>
    /// <returns>是否保存成功</returns>
    public bool SaveTaskData(List<ARTaskUIManager.TaskData> taskData)
    {
        if (taskData == null)
        {
            Debug.LogError("SaveTaskData: 任务数据为空。");
            return false;
        }
        
        try
        {
            var wrapper = new TaskListDataHelper { data = taskData };
            string json = JsonUtility.ToJson(wrapper, true); // true表示格式化JSON
            
            File.WriteAllText(taskDataFilePath, json);
            Debug.Log($"任务数据已保存至: {taskDataFilePath}");
            
            _cachedTaskData = taskData; // 更新缓存
            _isDataDirty = false; // 重置脏标记
            
            NotifyTaskDataUpdated(); // 通知监听器数据已更新
            
            return true;
        }
        catch (System.Exception e)
        {
            Debug.LogError($"保存任务数据失败: {e.Message}");
            return false;
        }
    }
    
    /// <summary>
    /// 标记数据为脏状态，强制下次加载时重新解析
    /// </summary>
    public void MarkDataDirty()
    {
        _isDataDirty = true;
        NotifyTaskDataUpdated();
    }
    
    /// <summary>
    /// 通知所有监听器任务数据已更新
    /// </summary>
    private void NotifyTaskDataUpdated()
    {
        OnTaskDataUpdated?.Invoke();
        
        // 尝试查找场景中的DetectionTest并通知它重新加载数据
        var detectionTests = FindObjectsOfType<DetectionTest>();
        foreach (var test in detectionTests)
        {
            if (test != null)
            {
                test.ReloadTagData();
                Debug.Log("已通知 DetectionTest 重新加载标签数据");
            }
        }
    }
}

// Helper class to parse JSON array (if defined inside ARTaskUIManager, make it public or move here)
// [System.Serializable]
// public class TaskListDataHelper
// {
//     public List<ARTaskUIManager.TaskData> data;
// } 