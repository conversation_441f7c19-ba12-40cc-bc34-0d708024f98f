/**
 * ARTaskUIManager.cs
 * 
 * 管理AR眼镜拍照UI系统，包括任务列表、工作区域状态提示和拍照完成效果
 * 任务数据从外部加载，UI显示任务的地点和内容。
 */

using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using System.Linq; // For Linq operations like FirstOrDefault
using DG.Tweening;
using System; // For System.Serializable

public class ARTaskUIManager : MonoBehaviour
{
    [Header("UI组件引用")]
    [SerializeField] private GameObject defaultTaskPanel;  // 默认任务栏面板
    [SerializeField] private GameObject arrivedWorkAreaPanel;  // 到达工作区域面板
    [SerializeField] private GameObject taskCompletedPanel;  // 任务完成面板
    [SerializeField] private GameObject photoPreviewPanel;  // 照片预览面板
    [SerializeField] private GameObject connectPromptPanel;  // 连接电脑提示文本
    [SerializeField] private GameObject guidePanel;  // 引导面板

    [Header("引导UI设置")]
    [SerializeField] private Image lookRightImage;  // 向右看引导图片
    [SerializeField] private Image moveRightImage;  // 向右移动引导图片
    [SerializeField] private Image lookLeftImage;   // 向左看引导图片
    [SerializeField] private Image moveLeftImage;   // 向左移动引导图片
    [SerializeField] private Image lookUpImage;     // 向上看引导图片
    [SerializeField] private Image moveUpImage;     // 向上移动引导图片
    [SerializeField] private Image lookDownImage;   // 向下看引导图片
    [SerializeField] private Image moveDownImage;   // 向下移动引导图片
    [SerializeField] private Image moveFrontImage;  // 向前移动引导图片
    [SerializeField] private Image moveBackImage;   // 向后移动引导图片

    [Header("任务列表设置")]
    [SerializeField] private Transform taskListContainer;  // 任务列表容器
    [SerializeField] private GameObject taskItemPrefab;  // 任务项目预制体 (应包含TaskListItem脚本)
    [SerializeField] private int maxDisplayTasks = 3;  // 最多显示任务数量

    [Header("照片预览设置")]
    [SerializeField] private Image photoPreviewImage;  // 照片预览图像
    [SerializeField] private float photoPreviewDuration = 2.5f;  // 照片预览持续时间    
    [SerializeField] private float photoScaleInDuration = 0.5f; // 照片放大动画时长
    [SerializeField] private float photoScaleOutDuration = 0.3f; // 照片缩小动画时长
    [SerializeField] private AnimationCurve photoScaleCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);  // 照片缩放曲线

    [Header("组件依赖")]
    [SerializeField] private WorkAreaDetector workAreaDetector;  // 工作区检测器引用，用于获取容差设置

    // 任务相关数据
    private List<TaskData> allTasks = new List<TaskData>();  // 所有任务
    private TaskData currentActiveTask = null; // 当前活动任务

    // 当前UI状态
    private UIState currentUIState = UIState.Default;
    
    // 照片预览活动标志，防止状态被中断
    private bool isPhotoPreviewActive = false;
    
    // UI状态枚举
    public enum UIState // Made public so WorkAreaDetector can potentially use it
    {
        Default,            // 默认状态（显示任务栏）
        ArrivedWorkArea,    // 到达工作区域状态
        Guide,              // 引导状态
        TaskCompleted       // 任务完成状态
    }

    // 引导方向枚举
    public enum GuideDirection
    {
        None,
        LookRight,
        MoveRight,
        LookLeft,
        MoveLeft,
        LookUp,
        MoveUp,
        LookDown,
        MoveDown,
        MoveFront,    // 新增：向前移动
        MoveBack      // 新增：向后移动
    }

    // 当前引导方向
    private GuideDirection currentGuideDirection = GuideDirection.None;

    // 后端Tag数据结构
    [System.Serializable]
    public class BackendTagData
    {
        public int tagId;
        public float posX;
        public float posY;
        public float posZ;
        public float rotX;
        public float rotY;
        public float rotZ;
        public string imgPath; // 新增字段，但不用于功能逻辑

        public Vector3 Position => new Vector3(posX, posY, posZ);
        public Quaternion Rotation => Quaternion.Euler(rotX, rotY, rotZ);
        
        // 用于跟踪此tagData是否已完成拍照
        [HideInInspector] public bool isCompleted = false;
    }

    // 任务数据结构 (对应后端JSON)
    [System.Serializable]
    public class TaskData
    {
        public int number;                  // 任务序号
        public string location;             // 任务地点
        public string content;              // 任务内容
        public List<BackendTagData> tagData; // 任务关联的Tag数据（现在支持多个）
        public bool isCompleted = false;     // 是否已完成（当所有tagData都完成时为true）
        public string additionalInformation; // 新增字段以匹配后端JSON
        
        // 用于UI显示的游戏对象引用
        [HideInInspector] public GameObject taskUIObject;
        [HideInInspector] public TaskListItem taskListItem;
        
        /// <summary>
        /// 检查是否所有tagData都已完成
        /// </summary>
        /// <returns>如果所有tagData都已完成，返回true</returns>
        public bool AreAllTagsCompleted()
        {
            if (tagData == null || tagData.Count == 0) return true;
            return tagData.All(tag => tag.isCompleted);
        }
        
        /// <summary>
        /// 获取未完成的tagData列表
        /// </summary>
        /// <returns>未完成的tagData列表</returns>
        public List<BackendTagData> GetIncompleteTagData()
        {
            if (tagData == null) return new List<BackendTagData>();
            return tagData.Where(tag => !tag.isCompleted).ToList();
        }
        
        /// <summary>
        /// 获取已完成的tagData数量
        /// </summary>
        /// <returns>已完成的tagData数量</returns>
        public int GetCompletedTagCount()
        {
            if (tagData == null) return 0;
            return tagData.Count(tag => tag.isCompleted);
        }
    }

    // 用于解析整个JSON数组
    [System.Serializable]
    private class TaskListDataHelper
    {
        public List<TaskData> data;
    }



    private void Start()
    {
        InitializeUI();
        LoadTasksFromBackend(); // Load tasks using the new BackendDataManager
        
        // 在编辑器模式下跳过电源状态检测，或者判断是否是第一次拍照应用
#if !UNITY_EDITOR
        // 只在非编辑器模式下订阅电源状态事件
        // 这是第二次拍照应用的UI管理器，不应影响第一次拍照应用
        if (WebCameraManager.Instance != null)
        {
            Debug.Log("ARTaskUIManager: 订阅电源状态事件");
            WebCameraManager.Instance.OnPowerConnected += HandlePowerConnected;
            WebCameraManager.Instance.OnPowerDisconnected += HandlePowerDisconnected;
        }
        else
        {
            Debug.LogWarning("ARTaskUIManager: WebCameraManager.Instance为空，无法订阅电源状态事件");
        }
#endif
    }
    
    private void OnDestroy()
    {
#if !UNITY_EDITOR
        // 取消订阅事件，只在非编辑器模式下需要执行
        if (WebCameraManager.Instance != null)
        {
            WebCameraManager.Instance.OnPowerConnected -= HandlePowerConnected;
            WebCameraManager.Instance.OnPowerDisconnected -= HandlePowerDisconnected;
        }
#endif
        
        if (taskListContainer != null)
        {
            foreach (Transform child in taskListContainer)
            {
                Destroy(child.gameObject);
            }
        }
        StopAllCoroutines();
        allTasks.Clear();
        currentActiveTask = null;
    }
    
    /// <summary>
    /// 处理电源连接事件
    /// </summary>
    public void HandlePowerConnected()
    {
        Debug.Log("ARTaskUIManager: 收到电源连接事件，正在处理UI更新");
        
        // 隐藏默认任务面板
        if (defaultTaskPanel != null)
        {
            defaultTaskPanel.SetActive(false);
            Debug.Log("ARTaskUIManager: 已隐藏DefaultTaskPanel");
        }
        else
        {
            Debug.LogWarning("ARTaskUIManager: defaultTaskPanel为空，无法隐藏");
        }
        
        // 显示连接提示文本
        if (connectPromptPanel != null)
        {
            connectPromptPanel.gameObject.SetActive(true);
            Debug.Log("ARTaskUIManager: 已显示ConnectPromptPanel");
        }
        else
        {
            Debug.LogWarning("ARTaskUIManager: connectPromptPanel为空，无法显示");
        }
    }
    
    /// <summary>
    /// 处理电源断开事件
    /// </summary>
    private void HandlePowerDisconnected()
    {
        // 如果当前在默认状态且满足其他条件，显示默认任务面板
        if (!isPhotoPreviewActive && currentUIState == UIState.Default && defaultTaskPanel != null)
        {
            defaultTaskPanel.SetActive(true);
        }
        
        // 隐藏连接提示文本
        if (connectPromptPanel != null)
        {
            connectPromptPanel.gameObject.SetActive(false);
        }
    }

    // 从JSON字符串加载任务数据
    public void LoadTasksFromJson(string json)
    {
        if (string.IsNullOrEmpty(json))
        {
            Debug.LogError("输入的JSON数据为空。");
            return;
        }

        try
        {
            TaskListDataHelper helper = JsonUtility.FromJson<TaskListDataHelper>(json);
            if (helper != null && helper.data != null)
            {
                allTasks = helper.data;
                // 默认激活第一个有未完成标签的任务
                currentActiveTask = allTasks.FirstOrDefault(t => !t.isCompleted && t.tagData != null && t.tagData.Any(tag => !tag.isCompleted));
                UpdateTaskListUI();
                SetUIState(UIState.Default);
                Debug.Log($"成功加载 {allTasks.Count} 个任务。");
            }
            else
            {
                Debug.LogError("JSON解析失败或数据格式不正确。");
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"JSON解析异常: {e.Message}");
        }
    }

    /// <summary>
    /// 照片预览动画协程，替代Update中的计时器
    /// </summary>
    private IEnumerator PhotoPreviewAnimation()
    {
        float timer = 0f;
        
        // 播放动画
        while (timer < photoPreviewDuration)
        {
            timer += Time.deltaTime;
            
            if (photoPreviewImage != null)
            {
                float scale = photoScaleCurve.Evaluate(timer / photoPreviewDuration);
                photoPreviewImage.transform.localScale = new Vector3(scale, scale, 1f);
            }
            
            yield return null;
        }
        
        // 动画结束
        photoPreviewPanel.SetActive(false);
        isPhotoPreviewActive = false;
        
        // 检查是否还有未完成的标签
        bool hasMoreIncompleteTags = allTasks.Any(t => !t.isCompleted && t.tagData != null && t.tagData.Any(tag => !tag.isCompleted));
        
        if (hasMoreIncompleteTags)
        {
            // 还有其他未完成的标签，显示任务完成面板
            ShowTaskCompletedUI();
            Debug.Log("照片预览结束，还有未完成的标签，显示任务完成面板");
        }
        else
        {
            // 所有任务的所有标签都已完成，直接关闭所有UI面板
            Debug.Log("所有任务的所有标签都已完成，照片预览结束后关闭所有UI面板");
            if(defaultTaskPanel != null) defaultTaskPanel.SetActive(false);
            if(arrivedWorkAreaPanel != null) arrivedWorkAreaPanel.SetActive(false);
            if(taskCompletedPanel != null) taskCompletedPanel.SetActive(false);
        }
    }

    // 初始化UI
    private void InitializeUI()
    {
        SetUIState(UIState.Default);
        
        // 确保照片预览面板初始隐藏
        if (photoPreviewPanel != null)
        {
            photoPreviewPanel.SetActive(false);
        }
        
        // 确保连接提示文本初始隐藏
        if (connectPromptPanel != null)
        {
            connectPromptPanel.gameObject.SetActive(false);
        }
        
        // 确保引导面板初始隐藏
        if (guidePanel != null)
        {
            guidePanel.SetActive(false);
        }
        
        // 重置所有引导图片
        ResetGuideImages();
    }

    // 设置UI状态 (仅控制面板显隐)
    private void SetUIState(UIState state)
    {
        // 如果当前在照片预览状态，不允许切换UI状态
        if (isPhotoPreviewActive)
        {
            Debug.Log("正在照片预览中，忽略UI状态切换请求");
            return;
        }
        
        currentUIState = state;
        
        // 强制重置所有面板状态
        if(defaultTaskPanel != null) defaultTaskPanel.SetActive(false);
        if(arrivedWorkAreaPanel != null) arrivedWorkAreaPanel.SetActive(false);
        if(taskCompletedPanel != null) taskCompletedPanel.SetActive(false);
        if(photoPreviewPanel != null) photoPreviewPanel.SetActive(false);
        if(guidePanel != null) guidePanel.SetActive(false); // 关闭引导面板
        
        // 根据状态显示相应面板
        switch (state)
        {
            case UIState.Default:
                if(defaultTaskPanel != null) defaultTaskPanel.SetActive(true);
                Debug.Log("UI切换到默认状态：显示任务列表");
                break;
                
            case UIState.ArrivedWorkArea:
                if(arrivedWorkAreaPanel != null) arrivedWorkAreaPanel.SetActive(true);
                Debug.Log("UI切换到工作区域状态：显示进入工作区提示");
                break;
                
            case UIState.Guide:
                if(guidePanel != null) guidePanel.SetActive(true);
                // 重置所有指引图片
                ResetGuideImages();
                Debug.Log("UI切换到引导状态：显示引导指示");
                break;
                
            case UIState.TaskCompleted:
                if(taskCompletedPanel != null) taskCompletedPanel.SetActive(true);
                Debug.Log("UI切换到任务完成状态：显示任务完成提示");
                break;
        }
    }

    // 更新任务列表UI
    private void UpdateTaskListUI()
    {
        if (taskListContainer == null || taskItemPrefab == null) return;

        // 清除现有任务项
        foreach (Transform child in taskListContainer)
        {
            Destroy(child.gameObject);
        }

        // 清除所有任务的UI引用
        foreach (var task in allTasks)
        {
            task.taskUIObject = null;
            task.taskListItem = null;
        }

        if (allTasks == null || !allTasks.Any()) return;

        int currentTaskIndex = currentActiveTask != null ? allTasks.IndexOf(currentActiveTask) : -1;
        if (currentTaskIndex == -1 && allTasks.Any(t => !t.isCompleted))
        {
            // 如果没有激活的任务，但有未完成的，则选择第一个未完成的
            currentActiveTask = allTasks.FirstOrDefault(t => !t.isCompleted);
            currentTaskIndex = currentActiveTask != null ? allTasks.IndexOf(currentActiveTask) : 0;
        }
        else if (currentTaskIndex == -1)
        {
             currentTaskIndex = 0; // fallback or all completed
        }
        
        List<TaskData> tasksToDisplay = new List<TaskData>();
        
        // 只显示有未完成标签的任务
        tasksToDisplay.AddRange(allTasks.Where(t => !t.isCompleted && t.tagData != null && t.tagData.Any(tag => !tag.isCompleted)).Take(maxDisplayTasks));
        
        // 创建任务UI项
        foreach (var taskData in tasksToDisplay)
        {
            GameObject taskItemGO = Instantiate(taskItemPrefab, taskListContainer);
            TaskListItem listItem = taskItemGO.GetComponent<TaskListItem>();
            
            if (listItem != null)
            {
                // 使用新的方法显示进度信息
                int completedCount = taskData.GetCompletedTagCount();
                int totalCount = taskData.tagData?.Count ?? 0;
                listItem.SetTaskText(taskData.number, taskData.location, taskData.content, completedCount, totalCount);
                listItem.SetActive(currentActiveTask != null && taskData.number == currentActiveTask.number);
                
                taskData.taskUIObject = taskItemGO;
                taskData.taskListItem = listItem;
            }
        }
        
        Debug.Log($"更新任务列表UI：显示 {tasksToDisplay.Count} 个有未完成标签的任务，总任务数：{allTasks.Count}，完全完成的任务数：{allTasks.Count(t => t.isCompleted)}");
    }

    // 外部调用：到达工作区域 (通过任务序号)
    public void OnArriveWorkArea(int taskNumber)
    {
        TaskData arrivedTask = allTasks.FirstOrDefault(t => t.number == taskNumber && !t.isCompleted);
        
        if (arrivedTask != null)
        {
            currentActiveTask = arrivedTask;
            SetUIState(UIState.ArrivedWorkArea);
            UpdateTaskListUI();
            
            // 2秒后自动切换到引导状态
            StartCoroutine(AutoShowGuideAfterDelay(1f));
        }
        else
        {
            Debug.LogWarning($"尝试到达任务 {taskNumber}，但未找到或已完成。");
        }
    }

    // 延迟显示引导界面
    private IEnumerator AutoShowGuideAfterDelay(float delay)
    {
        yield return new WaitForSeconds(delay);
        
        // 如果仍然在工作区域状态，切换到引导状态
        if (currentUIState == UIState.ArrivedWorkArea)
        {
            // 将UI状态切换到Guide，但不立即显示任何方向引导
            // 让WorkAreaDetector在下一帧提供基于实际位姿的引导
            SetUIState(UIState.Guide);
            Debug.Log("进入引导状态，等待WorkAreaDetector提供准确方向");
        }
    }

    public void OnPhotoTaken(Texture2D photoTexture)
    {
        if (currentActiveTask == null)
        {
            Debug.LogWarning("当前没有活动任务，无法处理拍照结果。");
            if (photoTexture != null) Destroy(photoTexture); // 清理传入的纹理
            return;
        }
        
        // 不再检查isCompleted状态，因为拍照时任务可能还未标记为完成
        // 确保关闭ArrivedWorkAreaPanel和GuidePanel
        if(arrivedWorkAreaPanel != null) 
        {
            arrivedWorkAreaPanel.SetActive(false);
        }
        
        if(guidePanel != null)
        {
            guidePanel.SetActive(false);
        }
        
        ShowPhotoPreview(photoTexture);
    }

    // 显示照片预览
    private void ShowPhotoPreview(Texture2D photoTexture)
    {
        if (photoPreviewPanel == null || photoPreviewImage == null) 
        {
            if (photoTexture != null) Destroy(photoTexture); // 清理纹理
            return;
        }

        if (photoTexture != null)
        {
            Sprite photoSprite = Sprite.Create(
                photoTexture, 
                new Rect(0, 0, photoTexture.width, photoTexture.height),
                new Vector2(0.5f, 0.5f)
            );
            photoPreviewImage.sprite = photoSprite;
        }
        else
        {
            Debug.LogWarning("传入的照片纹理为空。");
            // 可以设置一个默认的"无照片"图像或保持上一个
        }
        
        photoPreviewImage.transform.localScale = Vector3.zero;
        photoPreviewPanel.SetActive(true);
        
        // 设置照片预览活动标志
        isPhotoPreviewActive = true;
        
        // 确保所有其他面板关闭
        if (defaultTaskPanel != null) defaultTaskPanel.SetActive(false);
        if (arrivedWorkAreaPanel != null) arrivedWorkAreaPanel.SetActive(false);
        if (taskCompletedPanel != null) taskCompletedPanel.SetActive(false);
        
        // 启动照片预览动画协程
        StartCoroutine(PhotoPreviewAnimation());
        
        Debug.Log("开始照片预览，预览时长: " + photoPreviewDuration + "秒");
    }

    // 显示任务完成UI
    private void ShowTaskCompletedUI()
    {
        SetUIState(UIState.TaskCompleted);
        Invoke(nameof(ReturnToDefaultOrNextTaskState), 2f); // 2秒后返回默认状态
    }

    // 返回默认状态或下一个任务
    public void ReturnToDefaultOrNextTaskState()
    {
        // 如果当前在照片预览状态，不允许状态转换
        if (isPhotoPreviewActive)
        {
            Debug.Log("正在照片预览中，忽略状态转换请求");
            return;
        }
        
        // 查找下一个有未完成标签的任务
        currentActiveTask = allTasks.FirstOrDefault(t => !t.isCompleted && t.tagData != null && t.tagData.Any(tag => !tag.isCompleted));
        
        if (currentActiveTask != null)
        {
            // 如果还有未完成标签的任务，直接跳到默认状态，让WorkAreaDetector重新检测
            SetUIState(UIState.Default);
            Debug.Log($"切换到下一个有未完成标签的任务: 任务{currentActiveTask.number}，剩余标签: {currentActiveTask.GetIncompleteTagData().Count}");
        }
        else
        {
            // 所有任务的所有标签都已完成
            Debug.Log("所有任务的所有标签都已完成！");
            // 这里不再需要关闭所有面板，因为在照片预览结束时已经处理了
        }
        UpdateTaskListUI();
    }

    // 供WorkAreaDetector获取当前活动任务的Tag数据
    public BackendTagData GetCurrentActiveTaskTagData()
    {
        if (currentActiveTask != null && !currentActiveTask.isCompleted && currentActiveTask.tagData != null && currentActiveTask.tagData.Any())
        {
            // 返回第一个未完成的tagData
            return currentActiveTask.tagData.FirstOrDefault(tag => !tag.isCompleted);
        }
        return null;
    }
    
    /// <summary>
    /// 根据检测到的标签ID获取对应的目标数据（用于多标签任务）
    /// </summary>
    /// <param name="detectedTagId">检测到的标签ID</param>
    /// <returns>对应的目标标签数据，如果不匹配则返回null</returns>
    public BackendTagData GetTargetTagDataForDetectedTag(int detectedTagId)
    {
        if (currentActiveTask != null && !currentActiveTask.isCompleted && currentActiveTask.tagData != null && currentActiveTask.tagData.Any())
        {
            // 查找与检测到的标签ID匹配且未完成的tagData
            var targetTag = currentActiveTask.tagData.FirstOrDefault(tag => tag.tagId == detectedTagId && !tag.isCompleted);
            
            if (targetTag != null)
            {
                Debug.Log($"为检测到的标签ID {detectedTagId} 找到匹配的目标数据（任务 {currentActiveTask.number}）");
                return targetTag;
            }
            else
            {
                Debug.LogWarning($"检测到的标签ID {detectedTagId} 在当前活动任务 {currentActiveTask.number} 中未找到匹配的未完成标签");
                return null;
            }
        }
        return null;
    }
    
    /// <summary>
    /// 调试方法：显示当前活动任务的详细信息（用于多标签任务测试）
    /// </summary>
    public void DebugCurrentActiveTask()
    {
        if (currentActiveTask != null)
        {
            Debug.Log($"[调试] 当前活动任务: {currentActiveTask.number}");
            Debug.Log($"[调试] 任务完成状态: {currentActiveTask.isCompleted}");
            Debug.Log($"[调试] 总标签数: {currentActiveTask.tagData?.Count ?? 0}");
            
            if (currentActiveTask.tagData != null)
            {
                for (int i = 0; i < currentActiveTask.tagData.Count; i++)
                {
                    var tag = currentActiveTask.tagData[i];
                    Debug.Log($"[调试] 标签 {i+1}: ID={tag.tagId}, 完成={tag.isCompleted}");
                }
            }
            
            var firstIncomplete = GetCurrentActiveTaskTagData();
            if (firstIncomplete != null)
            {
                Debug.Log($"[调试] 默认返回的第一个未完成标签: ID={firstIncomplete.tagId}");
            }
        }
        else
        {
            Debug.Log("[调试] 当前无活动任务");
        }
    }
    
    public List<TaskData> GetIncompleteTasks()
    {
        // 返回未完成的任务（即那些还有未完成标签的任务）
        return allTasks.Where(t => !t.isCompleted).ToList();
    }
    
    /// <summary>
    /// 获取有未完成标签的任务列表（即使某些标签已完成）
    /// </summary>
    /// <returns>有未完成标签的任务列表</returns>
    public List<TaskData> GetTasksWithIncompleteTags()
    {
        return allTasks.Where(t => !t.isCompleted && t.tagData != null && t.tagData.Any(tag => !tag.isCompleted)).ToList();
    }
    
    /// <summary>
    /// 获取所有未完成的标签数据，包含其所属任务信息
    /// </summary>
    /// <returns>包含任务信息的未完成标签数据</returns>
    public List<(TaskData task, BackendTagData tagData)> GetAllIncompleteTagData()
    {
        var result = new List<(TaskData, BackendTagData)>();
        foreach (var task in allTasks.Where(t => !t.isCompleted))
        {
            if (task.tagData != null)
            {
                foreach (var tag in task.tagData.Where(tag => !tag.isCompleted))
                {
                    result.Add((task, tag));
                }
            }
        }
        return result;
    }

    /// <summary>
    /// 标记指定任务的特定tagData为已完成
    /// </summary>
    /// <param name="taskNumber">任务编号</param>
    /// <param name="tagId">标签ID</param>
    public void MarkTagAsCompleted(int taskNumber, int tagId)
    {
        TaskData task = allTasks.FirstOrDefault(t => t.number == taskNumber);
        if (task != null && task.tagData != null)
        {
            BackendTagData targetTag = task.tagData.FirstOrDefault(tag => tag.tagId == tagId);
            if (targetTag != null && !targetTag.isCompleted)
            {
                targetTag.isCompleted = true;
                Debug.Log($"任务 {taskNumber} 的标签 {tagId} 已标记为完成。已完成: {task.GetCompletedTagCount()}/{task.tagData.Count}");
                
                // 检查整个任务是否已完成
                if (task.AreAllTagsCompleted())
                {
                    task.isCompleted = true;
                    Debug.Log($"任务 {taskNumber} 的所有标签都已完成，任务标记为完成。");
                    
                    // 销毁已完成任务的UI预制体
                    if (task.taskUIObject != null)
                    {
                        Debug.Log($"销毁已完成任务 {taskNumber} 的UI预制体");
                        Destroy(task.taskUIObject);
                        task.taskUIObject = null;
                        task.taskListItem = null;
                    }
                    
                    // 立即更新任务列表UI，确保UI与数据状态一致
                    UpdateTaskListUI();
                }
            }
            else if (targetTag == null)
            {
                Debug.LogWarning($"在任务 {taskNumber} 中未找到标签 {tagId}");
            }
            else
            {
                Debug.Log($"任务 {taskNumber} 的标签 {tagId} 已经完成过了");
            }
        }
        else if (task == null)
        {
            Debug.LogWarning($"未找到任务 {taskNumber}");
        }
    }

    /// <summary>
    /// 供WorkAreaDetector标记任务完成（保留向后兼容性）
    /// 此方法将标记任务的第一个未完成标签为完成
    /// </summary>
    /// <param name="taskNumber">任务编号</param>
    [System.Obsolete("请使用MarkTagAsCompleted(int taskNumber, int tagId)替代")]
    public void MarkTaskAsCompleted(int taskNumber)
    {
        TaskData task = allTasks.FirstOrDefault(t => t.number == taskNumber);
        if (task != null && task.tagData != null)
        {
            var incompleteTag = task.tagData.FirstOrDefault(tag => !tag.isCompleted);
            if (incompleteTag != null)
            {
                MarkTagAsCompleted(taskNumber, incompleteTag.tagId);
            }
            else
            {
                Debug.LogWarning($"任务 {taskNumber} 没有未完成的标签");
            }
        }
    }

    private void LoadTasksFromBackend()
    {
        var tasks = BackendDataManager.Instance.LoadAndParseTaskData();
        
        if (tasks != null && tasks.Count > 0)
        {
            allTasks = tasks;
            // 默认激活第一个有未完成标签的任务
            currentActiveTask = allTasks.FirstOrDefault(t => !t.isCompleted && t.tagData != null && t.tagData.Any(tag => !tag.isCompleted));
            UpdateTaskListUI();
            SetUIState(UIState.Default); // 加载后初始化为默认状态
            Debug.Log($"成功通过BackendDataManager加载了 {allTasks.Count} 个任务。");
            
            // 添加详细日志，显示任务编号、位置、内容和标签数量
            foreach (var task in allTasks)
            {
                int tagCount = task.tagData?.Count ?? 0;
                Debug.Log($"加载任务: 编号={task.number}, 位置={task.location}, 内容={task.content}, 标签数量={tagCount}");
                
                // 显示每个标签的详细信息
                if (task.tagData != null)
                {
                    foreach (var tag in task.tagData)
                    {
                        Debug.Log($"  标签ID={tag.tagId}, 位置=({tag.posX:F3}, {tag.posY:F3}, {tag.posZ:F3})");
                    }
                }
            }
            
            // 检查电源连接状态，如果已连接则立即处理UI
            if (WebCameraManager.Instance != null && WebCameraManager.Instance.IsPowerCurrentlyConnected)
            {
                Debug.Log("检测到电源已连接，主动调用HandlePowerConnected方法");
                HandlePowerConnected();
            }
        }
        else
        {
            Debug.Log("任务数据文件未找到或为空，进入第一次拍照阶段。");
            // 进入第一次拍照阶段，关闭默认任务面板
            if (defaultTaskPanel != null)
            {
                defaultTaskPanel.SetActive(false);
            }
        }
    }

    // 重置所有引导图片
    private void ResetGuideImages()
    {
        if(lookRightImage) lookRightImage.gameObject.SetActive(false);
        if(moveRightImage) moveRightImage.gameObject.SetActive(false);
        if(lookLeftImage) lookLeftImage.gameObject.SetActive(false);
        if(moveLeftImage) moveLeftImage.gameObject.SetActive(false);
        if(lookUpImage) lookUpImage.gameObject.SetActive(false);
        if(moveUpImage) moveUpImage.gameObject.SetActive(false);
        if(lookDownImage) lookDownImage.gameObject.SetActive(false);
        if(moveDownImage) moveDownImage.gameObject.SetActive(false);
        if(moveFrontImage) moveFrontImage.gameObject.SetActive(false);
        if(moveBackImage) moveBackImage.gameObject.SetActive(false);
        
        currentGuideDirection = GuideDirection.None;
    }
    
    /// <summary>
    /// 显示指定方向的引导图片
    /// </summary>
    /// <param name="direction">引导方向</param>
    public void ShowGuide(GuideDirection direction)
    {
        // 如果不在Guide状态，先切换到Guide状态
        if (currentUIState != UIState.Guide)
        {
            SetUIState(UIState.Guide);
        }
        
        // 重置所有引导图片
        ResetGuideImages();
        
        // 设置当前引导方向
        currentGuideDirection = direction;
        
        // 激活相应的引导图片
        switch (direction)
        {
            case GuideDirection.LookRight:
                if(lookRightImage) lookRightImage.gameObject.SetActive(true);
                break;
            case GuideDirection.MoveRight:
                if(moveRightImage) moveRightImage.gameObject.SetActive(true);
                break;
            case GuideDirection.LookLeft:
                if(lookLeftImage) lookLeftImage.gameObject.SetActive(true);
                break;
            case GuideDirection.MoveLeft:
                if(moveLeftImage) moveLeftImage.gameObject.SetActive(true);
                break;
            case GuideDirection.LookUp:
                if(lookUpImage) lookUpImage.gameObject.SetActive(true);
                break;
            case GuideDirection.MoveUp:
                if(moveUpImage) moveUpImage.gameObject.SetActive(true);
                break;
            case GuideDirection.LookDown:
                if(lookDownImage) lookDownImage.gameObject.SetActive(true);
                break;
            case GuideDirection.MoveDown:
                if(moveDownImage) moveDownImage.gameObject.SetActive(true);
                break;
            case GuideDirection.MoveFront:
                if(moveFrontImage) moveFrontImage.gameObject.SetActive(true);
                break;
            case GuideDirection.MoveBack:
                if(moveBackImage) moveBackImage.gameObject.SetActive(true);
                break;
            case GuideDirection.None:
                // 什么都不显示
                break;
        }
        
        Debug.Log($"显示引导：{direction}");
    }
    
    /// <summary>
    /// 关闭引导面板
    /// </summary>
    public void CloseGuide()
    {
        if (guidePanel != null)
        {
            guidePanel.SetActive(false);
            currentGuideDirection = GuideDirection.None;
            Debug.Log("关闭引导面板");
        }
    }

    /// <summary>
    /// 根据识别结果更新引导方向，用于WorkAreaDetector等外部调用
    /// </summary>
    /// <param name="offsetX">X轴偏移量（左右方向）</param>
    /// <param name="offsetY">Y轴偏移量（上下方向）</param>
    /// <param name="offsetZ">Z轴偏移量（前后方向）</param>
    /// <param name="angleOffset">角度偏移量</param>
    public void UpdateGuideDirectionByOffset(float offsetX, float offsetY, float offsetZ, float angleOffset)
    {
        // 如果不在引导状态，不进行处理
        if (currentUIState != UIState.Guide)
        {
            return;
        }
        
        // 获取WorkAreaDetector的容差设置
        float positionThreshold; 
        float angleThreshold;

        // 使用WorkAreaDetector的容差值的1.2倍作为引导阈值
        // 这样确保用户有足够的缓冲空间，避免引导与匹配条件矛盾
        positionThreshold = workAreaDetector.GetPositionTolerance() * 1.2f;
        angleThreshold = workAreaDetector.GetRotationTolerance() * 1.0f;
        
        Debug.Log($"[引导阈值] 位置阈值: {positionThreshold:F3}m, 角度阈值: {angleThreshold:F1}°");
        Debug.Log($"[当前偏移] X: {offsetX:F3}m, Y: {offsetY:F3}m, Z: {offsetZ:F3}m, 角度: {angleOffset:F1}°");

        // 创建偏移量数组并按绝对值大小排序，优先处理最大的偏移
        var offsets = new[]
        {
            new { Axis = "X", Value = offsetX, AbsValue = Mathf.Abs(offsetX) },
            new { Axis = "Y", Value = offsetY, AbsValue = Mathf.Abs(offsetY) },
            new { Axis = "Z", Value = offsetZ, AbsValue = Mathf.Abs(offsetZ) }
        };
        
        // 按绝对值大小降序排列
        System.Array.Sort(offsets, (a, b) => b.AbsValue.CompareTo(a.AbsValue));
        
        GuideDirection newDirection = GuideDirection.None;
        
        // 优先处理位置偏移（按偏移量大小排序）
        foreach (var offset in offsets)
        {
            if (offset.AbsValue > positionThreshold)
            {
                switch (offset.Axis)
                {
                    case "X": // X轴偏移 → 左右移动引导
                        if (offset.Value > 0)
                        {
                            newDirection = GuideDirection.MoveRight;
                            Debug.Log($"X轴偏移: {offset.Value:F3}米，提示向右移动（阈值:{positionThreshold:F3}米）");
                        }
                        else
                        {
                            newDirection = GuideDirection.MoveLeft;
                            Debug.Log($"X轴偏移: {offset.Value:F3}米，提示向左移动（阈值:{positionThreshold:F3}米）");
                        }
                        break;
                        
                    case "Y": // Y轴偏移 → 上下移动引导
                        if (offset.Value > 0)
                        {
                            newDirection = GuideDirection.MoveUp;
                            Debug.Log($"Y轴偏移: {offset.Value:F3}米，提示向上移动（阈值:{positionThreshold:F3}米）");
                        }
                        else
                        {
                            newDirection = GuideDirection.MoveDown;
                            Debug.Log($"Y轴偏移: {offset.Value:F3}米，提示向下移动（阈值:{positionThreshold:F3}米）");
                        }
                        break;
                        
                    case "Z": // Z轴偏移 → 前后移动引导
                        if (offset.Value > 0)
                        {
                            newDirection = GuideDirection.MoveFront;
                            Debug.Log($"Z轴偏移: {offset.Value:F3}米，提示向前移动（阈值:{positionThreshold:F3}米）");
                        }
                        else
                        {
                            newDirection = GuideDirection.MoveBack;
                            Debug.Log($"Z轴偏移: {offset.Value:F3}米，提示向后移动（阈值:{positionThreshold:F3}米）");
                        }
                        break;
                }
                break; // 找到最大偏移后立即跳出
            }
        }
        
        // 如果位置偏移都在容差范围内，检查角度偏移
        if (newDirection == GuideDirection.None && Mathf.Abs(angleOffset) > angleThreshold)
        {
            if (angleOffset < 0)
            {
                newDirection = GuideDirection.LookRight;
                Debug.Log($"角度偏移: {angleOffset:F1}度（负值），提示向右看（阈值:{angleThreshold:F1}度）");
            }
            else
            {
                if (angleOffset > 150f)
                {
                    newDirection = GuideDirection.LookLeft;
                    Debug.Log($"角度偏移: {angleOffset:F1}度（大幅正偏移），提示向左看（阈值:{angleThreshold:F1}度）");
                }
                else
                {
                    newDirection = GuideDirection.LookLeft;
                    Debug.Log($"角度偏移: {angleOffset:F1}度（正值），提示向左看（阈值:{angleThreshold:F1}度）");
                }
            }
        }
        
        // 如果所有偏移都在容差范围内，关闭引导
        if (newDirection == GuideDirection.None)
        {
            CloseGuide();
            Debug.Log($"[引导决策] 所有偏移量在容差范围内，关闭引导。位置偏移: X={Mathf.Abs(offsetX):F3}, Y={Mathf.Abs(offsetY):F3}, Z={Mathf.Abs(offsetZ):F3}, 角度偏移: {Mathf.Abs(angleOffset):F1}");
            return;
        }
        
        // 直接显示新的引导方向（移除多帧验证）
        if (newDirection != currentGuideDirection)
        {
            Debug.Log($"[引导更新] 引导方向从 {currentGuideDirection} 变更为 {newDirection}");
            ShowGuide(newDirection);
        }
    }
    
    /// <summary>
    /// 通知识别成功，关闭引导并继续拍照流程
    /// </summary>
    public void NotifyRecognitionSuccess()
    {
        // 关闭引导面板
        CloseGuide();
        Debug.Log("识别成功，关闭引导面板继续拍照流程");
    }
}