/**
 * AprilTagDetector.cs
 * 
 * 专门负责AprilTag的检测和文件命名格式，
 * 为第一次拍照应用提供支持，不依赖BackendDataManager。
 * 使用单例模式提供全局访问点。
 */
using UnityEngine;
using System.Collections.Generic;
using System.Text;
using AprilTag;
using System.Linq;
using System;
using UnityEngine.UI;

public class AprilTagDetector : MonoBehaviour
{
    // --- Singleton Pattern ---
    private static AprilTagDetector _instance;
    public static AprilTagDetector Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = FindObjectOfType<AprilTagDetector>();
                if (_instance == null)
                {
                    GameObject singletonObject = new GameObject("AprilTagDetector");
                    _instance = singletonObject.AddComponent<AprilTagDetector>();
                    DontDestroyOnLoad(singletonObject);
                    Debug.Log("AprilTagDetector instance created.");
                }
            }
            return _instance;
        }
    }

    [Header("AprilTag检测设置")]
    [SerializeField] private int decimation = 2; // 使用较小值提高检测率
    [SerializeField] private float tagSize = 0.05f;
    
    public enum PhotoFormat { Png, Jpg }
    [SerializeField] private PhotoFormat photoFormat = PhotoFormat.Png;
    
    [Header("UI引用")]
    [SerializeField] private RawImage previewRawImage; // 用于显示预览的RawImage组件
    
    // 内部变量
    private AprilTag.TagDetector _detector;
    private WebCameraManager _cameraManager;
    private bool _isInitialized = false;

    // AprilTag检测结果缓存
    private List<AprilTag.TagPose> _detectedTags = new List<AprilTag.TagPose>();
    private int _framesSinceLastDebugLog = 0;
    private int _debugLogInterval = 30; // 每30帧输出一次调试信息

    private void Awake()
    {
        if (_instance != null && _instance != this)
        {
            Destroy(gameObject);
            return;
        }
        _instance = this;
        DontDestroyOnLoad(gameObject);
    }

    private void Start()
    {
        // 获取WebCameraManager实例
        _cameraManager = WebCameraManager.Instance;
    }

    /// <summary>
    /// 初始化AprilTag检测器
    /// </summary>
    /// <returns>是否成功初始化</returns>
    public bool InitializeDetector()
    {
        if (_isInitialized && _detector != null)
        {
            return true;
        }

        try
        {
            // 获取相机分辨率
            Vector2Int resolution = _cameraManager.GetCameraResolution();
            
            // 创建新的检测器
            _detector = new AprilTag.TagDetector(resolution.x, resolution.y, decimation);
            _isInitialized = true;
            
            Debug.Log($"AprilTag检测器已初始化: {resolution.x}x{resolution.y}, decimation={decimation}");
            return true;
        }
        catch (Exception e)
        {
            Debug.LogError($"初始化AprilTag检测器失败: {e.Message}");
            _isInitialized = false;
            return false;
        }
    }

    /// <summary>
    /// 设置要检测的RawImage组件
    /// </summary>
    public void SetPreviewRawImage(RawImage rawImage)
    {
        previewRawImage = rawImage;
    }

    private void OnDestroy()
    {
        if (_detector != null)
        {
            _detector.Dispose();
            _detector = null;
        }
    }

    /// <summary>
    /// LateUpdate方法，在相机更新后执行AprilTag检测
    /// </summary>
    private void LateUpdate()
    {
        // 如果检测器未初始化或相机未就绪，不执行检测
        if (_detector == null || _cameraManager == null || !_cameraManager.IsCameraReady)
        {
            return;
        }

        // 更新预览（如果有）
        if (previewRawImage != null)
        {
            previewRawImage.texture = _cameraManager.WebCamTexture;
        }
        
        // 获取图像数据
        var texture = _cameraManager.WebCamTexture;
        if (texture == null) return;
        
        var image = texture.AsSpan();
        if (image.IsEmpty) return;

        // 执行AprilTag检测
        DetectAprilTags(image);
    }
    
    /// <summary>
    /// 执行AprilTag检测
    /// </summary>
    private void DetectAprilTags(System.ReadOnlySpan<Color32> image)
    {
        try
        {
            // 获取相机FOV
            float fov = Camera.main != null 
                ? Camera.main.fieldOfView * Mathf.Deg2Rad 
                : 60f * Mathf.Deg2Rad;
            
            // 处理图像
            _detector.ProcessImage(image, fov, tagSize);
            
            // 保存检测结果
            _detectedTags = _detector.DetectedTags.ToList();
            
            // 输出检测结果（限制频率）
            if (_detectedTags.Count > 0) 
            {
                Debug.Log($"检测到 {_detectedTags.Count} 个AprilTag: ID为 {string.Join(", ", _detectedTags.Select(t => t.ID))}");
            }
            else if (_framesSinceLastDebugLog >= _debugLogInterval)
            {
                Debug.Log("未检测到AprilTag");
                _framesSinceLastDebugLog = 0;
            }
            _framesSinceLastDebugLog++;
        }
        catch (Exception e)
        {
            Debug.LogError($"AprilTag检测过程中发生错误: {e.Message}");
        }
    }

    /// <summary>
    /// 捕获当前帧并执行AprilTag检测
    /// </summary>
    /// <returns>检测到的Tags数组</returns>
    public AprilTag.TagPose[] CaptureAndDetect()
    {
        // 确保检测器已初始化
        if (!_isInitialized)
        {
            InitializeDetector();
        }
        
        if (_detector == null || _cameraManager == null || !_cameraManager.IsCameraReady)
        {
            Debug.LogWarning("检测器或相机未准备好，无法进行AprilTag检测");
            return new AprilTag.TagPose[0];
        }
        
        Debug.Log("正在执行AprilTag捕获和检测...");
        
        try
        {
            // 捕获帧（使用相机管理器的当前帧）
            Texture2D frameTexture = _cameraManager.CaptureFrame();
            if (frameTexture == null)
            {
                Debug.LogError("无法捕获相机帧");
                return new AprilTag.TagPose[0];
            }
            
            var span = frameTexture.AsSpan();
            if (span.IsEmpty)
            {
                Debug.LogError("捕获的帧数据为空");
                Destroy(frameTexture);
                return new AprilTag.TagPose[0];
            }
            
            // 执行AprilTag检测
            DetectAprilTags(span);
            
            // 清理资源
            Destroy(frameTexture);
            
            // 返回检测结果
            return _detectedTags.ToArray();
        }
        catch (Exception e)
        {
            Debug.LogError($"CaptureAndDetect过程中发生错误: {e.Message}");
            return new AprilTag.TagPose[0];
        }
    }
    
    /// <summary>
    /// 捕获照片并保存，集成所有检测和保存流程
    /// </summary>
    /// <param name="photoName">输出照片名称</param>
    /// <param name="photoTexture">输出捕获的照片纹理</param>
    /// <returns>是否成功捕获和保存照片</returns>
    public bool CapturePhotoWithTag(out string photoName, out Texture2D photoTexture)
    {
        photoName = "";
        photoTexture = null;
        
        // 确保检测器已初始化
        if (!_isInitialized)
        {
            InitializeDetector();
        }
        
        // 检查相机状态
        if (_cameraManager == null || !_cameraManager.IsCameraReady)
        {
            Debug.LogWarning("相机未就绪，无法拍照");
            return false;
        }
        
        try
        {
            // 捕获照片
            photoTexture = _cameraManager.CaptureFrame();
            if (photoTexture == null)
            {
                Debug.LogError("捕获照片失败");
                return false;
            }
            
            Debug.Log($"照片捕获成功: {photoTexture.width}x{photoTexture.height}");
            AprilTag.TagPose? detectedTagPose = null;
            if (_detectedTags.Count > 0)
            {
                detectedTagPose = _detectedTags[0]; // 使用第一个检测到的Tag
                Debug.Log($"使用已检测到的AprilTag，TagID: {detectedTagPose.Value.ID}");
            }
            else
            {
                Debug.Log("未检测到AprilTag，将使用默认命名");
            }
            
            // 生成文件名
            photoName = GenerateFileName(detectedTagPose);
            
            return true;
        }
        catch (Exception e)
        {
            Debug.LogError($"拍照过程发生错误: {e.Message}");
            return false;
        }
    }

    /// <summary>
    /// 获取最近一次检测的结果
    /// </summary>
    public AprilTag.TagPose[] DetectedTags => _detectedTags.ToArray();
    
    /// <summary>
    /// 检测器是否已初始化
    /// </summary>
    public bool IsInitialized => _isInitialized && _detector != null;

    /// <summary>
    /// 根据检测到的Tag生成文件名
    /// </summary>
    /// <param name="tagPose">检测到的TagPose，如果为null则表示未检测到</param>
    /// <returns>生成的文件名</returns>
    public string GenerateFileName(AprilTag.TagPose? tagPose)
    {
        StringBuilder sb = new StringBuilder();
        
        if (tagPose.HasValue)
        {
            // 检测到tag，使用标准命名格式（不添加时间戳）
            AprilTag.TagPose pose = tagPose.Value;
            sb.Append($"TagID={pose.ID}_");
            sb.Append($"PosX={pose.Position.x.ToString("F6", System.Globalization.CultureInfo.InvariantCulture).Replace('.', ',')}_");
            sb.Append($"PosY={pose.Position.y.ToString("F6", System.Globalization.CultureInfo.InvariantCulture).Replace('.', ',')}_");
            sb.Append($"PosZ={pose.Position.z.ToString("F6", System.Globalization.CultureInfo.InvariantCulture).Replace('.', ',')}_");
            // 添加旋转信息 (转换为欧拉角)
            Vector3 eulerAngles = pose.Rotation.eulerAngles;
            sb.Append($"RotX={eulerAngles.x.ToString("F6", System.Globalization.CultureInfo.InvariantCulture).Replace('.', ',')}_");
            sb.Append($"RotY={eulerAngles.y.ToString("F6", System.Globalization.CultureInfo.InvariantCulture).Replace('.', ',')}_");
            sb.Append($"RotZ={eulerAngles.z.ToString("F6", System.Globalization.CultureInfo.InvariantCulture).Replace('.', ',')}");
        }
        else
        {
            // 未检测到tag，添加时间戳避免文件覆盖
            string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            sb.Append($"{timestamp}_NoTagDetected");
        }

        // 添加文件扩展名
        sb.Append(photoFormat == PhotoFormat.Png ? ".png" : ".jpg");

        return sb.ToString();
    }
} 