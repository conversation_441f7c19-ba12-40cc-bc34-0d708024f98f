/**
 * UIManager.cs
 * 
 * 第一次拍照应用的UI管理器
 * 负责显示应用状态、操作提示和通知信息
 */
using UnityEngine;
using UnityEngine.UI;
using System.Collections;
using DG.Tweening;
using TMPro;

public class UIManager : MonoBehaviour
{
    [Header("UI组件")]
    [SerializeField] private TextMeshProUGUI notificationText; // 通知文本
    [SerializeField] private Image notificationBackground; // 通知背景
    
    [Header("通知设置")]
    [SerializeField] private float notificationDuration = 2f; // 通知显示持续时间
    [SerializeField] private float fadeInDuration = 0.5f; // 淡入时间
    [SerializeField] private float fadeOutDuration = 0.5f; // 淡出时间
    
    private Tween notificationTween;

    void Start()
    {
        // 隐藏通知
        if (notificationText != null)
        {
            notificationText.gameObject.SetActive(false);
        }
        
        if (notificationBackground != null)
        {
            notificationBackground.gameObject.SetActive(false);
        }
    }
    
    /// <summary>
    /// 显示通知信息
    /// </summary>
    /// <param name="message">通知消息</param>
    /// <param name="backgroundColor">通知背景颜色，默认为null表示使用当前颜色</param>
    public void ShowNotification(string message, Color? backgroundColor = null)
    {
        // 如果已有通知在显示，先停止
        notificationTween.Kill();
        
        // 显示通知文本和背景
        if (notificationText != null)
        {
            notificationText.text = message;
            notificationText.gameObject.SetActive(true);
            
            // 设置初始透明度
            Color textColor = notificationText.color;
            textColor.a = 0;
            notificationText.color = textColor;
        }
        
        // 显示背景

        notificationBackground.gameObject.SetActive(true);
            
        // 如果提供了背景颜色，则设置背景颜色
        if (backgroundColor.HasValue)
            {
                Color bgColor = backgroundColor.Value;
                bgColor.a = 0; // 初始透明
                notificationBackground.color = bgColor;
            }
            else
            {
                // 设置初始透明度
                Color bgColor = notificationBackground.color;
                bgColor.a = 0;
                notificationBackground.color = bgColor;
            }
        
        
        // 创建动画序列
        Sequence sequence = DOTween.Sequence();
        
        sequence.Join(notificationText.DOFade(1f, fadeInDuration));
        
            // 使用提供的背景颜色或默认透明度
        float targetAlpha = backgroundColor.HasValue ? 1f : 0.8f;
        sequence.Join(notificationBackground.DOFade(targetAlpha, fadeInDuration));

        
        // 添加等待时间
        sequence.AppendInterval(notificationDuration);
        
        // 添加淡出动画
        sequence.Append(notificationText.DOFade(0f, fadeOutDuration));
        
        sequence.Join(notificationBackground.DOFade(0f, fadeOutDuration));
        
        
        // 添加完成回调
        sequence.OnComplete(() => {
            notificationText.gameObject.SetActive(false);
            
            notificationBackground.gameObject.SetActive(false);
            
            notificationTween = null;
        });
        
        // 保存动画引用
        notificationTween = sequence;
    }
} 