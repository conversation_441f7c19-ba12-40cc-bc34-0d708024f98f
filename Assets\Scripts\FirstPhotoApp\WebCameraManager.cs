/**
 * WebCameraManager.cs
 * 
 * 相机管理器组件，负责初始化WebCamTexture并提供访问接口。
 * 取代Klak.TestTools.ImageSource，提供更直接稳定的相机访问方式。
 */
using UnityEngine;
using UnityEngine.UI;
using System;
using UnityEngine.SceneManagement; // 添加命名空间

public class WebCameraManager : MonoBehaviour
{
    // --- Singleton Pattern ---
    private static WebCameraManager _instance;
    public static WebCameraManager Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = FindObjectOfType<WebCameraManager>();
                if (_instance == null)
                {
                    GameObject singletonObject = new GameObject("WebCameraManager");
                    _instance = singletonObject.AddComponent<WebCameraManager>();
                    DontDestroyOnLoad(singletonObject);
                    Debug.Log("WebCameraManager instance created.");
                }
            }
            return _instance;
        }
    }
    
    // --- 事件声明 ---
    // 电源连接事件
    public event Action OnPowerConnected;
    // 电源断开事件
    public event Action OnPowerDisconnected;

    [Header("相机设置")]
    [SerializeField] private int cameraWidth = 1920;  
    [SerializeField] private int cameraHeight = 1080;
    [SerializeField] private int cameraFrameRate = 30;
    [SerializeField] private string preferredCamera = "";
    [SerializeField] private RawImage previewImage; // 可选的预览UI组件

    private WebCamTexture webCamTexture;
    private bool isCapturingFrame = false;
    private bool wasConnectedToPower = false; // 用于跟踪上一帧的电源连接状态
    private bool _shouldCameraBeDisabled = false; // 控制相机是否应该保持禁用状态

    // 暴露相机纹理供外部访问
    public WebCamTexture WebCamTexture => webCamTexture;

    // 相机是否准备好
    public bool IsCameraReady => webCamTexture != null && webCamTexture.isPlaying;

    public bool IsPowerCurrentlyConnected { get; private set; }
    
    // 获取当前相机分辨率
    public Vector2Int GetCameraResolution()
    {
        if (webCamTexture != null && webCamTexture.isPlaying)
        {
            return new Vector2Int(webCamTexture.width, webCamTexture.height);
        }
        return new Vector2Int(cameraWidth, cameraHeight);
    }
    
    /// <summary>
    /// 检查是否应该启用电源状态检测
    /// 在Unity编辑器中返回false（不检测），在打包后返回true（检测）
    /// </summary>
    /// <returns>是否应该启用电源检测</returns>
    private bool ShouldEnablePowerDetection()
    {
#if UNITY_EDITOR
        Debug.Log("电源检测：当前在Unity编辑器中，跳过电源状态检测");
        return false; // 编辑器中不检测电源
#else
        Debug.Log("电源检测：当前在打包环境中，启用电源状态检测");
        return true; // 打包后检测电源
#endif
    }
    
    /// <summary>
    /// 检查当前是否为第一次拍照应用
    /// </summary>
    /// <returns>如果当前场景中存在FirstPhotoManager对象，返回true</returns>
    private bool IsFirstPhotoApp()
    {
        bool isFirstApp = FindObjectOfType<FirstPhotoManager>() != null;
        // 检测第二次拍照应用的标志：ARTaskUIManager
        bool hasARTaskUI = FindObjectOfType<ARTaskUIManager>() != null;
        
        // 如果有ARTaskUIManager，则我们处于第二次拍照应用中
        if (hasARTaskUI)
        {
            Debug.Log($"电源状态检测：检测到ARTaskUIManager，确认为第二次拍照应用，将进行电源状态检测");
            return false; // 返回false以启用电源状态检测
        }
        
        Debug.Log($"电源状态检测：{(isFirstApp ? "是" : "否")} 第一次拍照应用");
        return isFirstApp;
    }
    
    private void Awake()
    {
        if (_instance != null && _instance != this)
        {
            Destroy(gameObject);
            return;
        }
        _instance = this;
        DontDestroyOnLoad(gameObject);
    }

    private void Start()
    {
        // 检查是否应该启用电源检测
        if (ShouldEnablePowerDetection())
        {
            //检查初始电源状态
            BatteryStatus initialBatteryStatus = SystemInfo.batteryStatus;
            IsPowerCurrentlyConnected = (initialBatteryStatus == BatteryStatus.Charging || initialBatteryStatus == BatteryStatus.Full);
            wasConnectedToPower = IsPowerCurrentlyConnected;
            
            Debug.Log($"WebCameraManager初始化: 初始电源状态为 {(IsPowerCurrentlyConnected ? "已连接" : "未连接")}");
            
            // 如果初始电源已连接且不是第一次拍照应用，通过统一的处理方法处理
            if (IsPowerCurrentlyConnected && !IsFirstPhotoApp())
            {
                Debug.Log("初始电源已连接，调用统一的电源连接处理方法");
                HandlePowerConnected();
                return; // 电源已连接，不初始化相机
            }
        }
        else
        {
            // 编辑器模式下，设置默认值并跳过电源检测
            IsPowerCurrentlyConnected = false;
            wasConnectedToPower = false;
            Debug.Log("WebCameraManager初始化: 编辑器模式，跳过电源状态检测");
        }
        
        // 初始化相机
        InitializeCamera();
    }

    private void Update()
    {
        // 只在非第一次拍照应用中且启用电源检测时检查电源状态
        if (ShouldEnablePowerDetection() && !IsFirstPhotoApp())
        {
            // 检查电源状态
            CheckPowerStatus();
        }
        
        if (!_shouldCameraBeDisabled && (webCamTexture == null || !webCamTexture.isPlaying))
        {
            if (!IsPowerCurrentlyConnected)
            {
                InitializeCamera();
            }
        }
        
        // 如果相机已禁用但仍在运行，强制停止
        if (_shouldCameraBeDisabled && webCamTexture != null && webCamTexture.isPlaying)
        {
            ReleaseCamera();
        }
        
        // 更新预览图像（如果有）
        UpdatePreviewImage();
    }
    
    /// <summary>
    /// 检查电源状态并在状态变化时触发事件
    /// </summary>
    private void CheckPowerStatus()
    {
        BatteryStatus currentBatteryStatus = SystemInfo.batteryStatus;
        bool isCurrentlyConnectedToPower = (currentBatteryStatus == BatteryStatus.Charging || currentBatteryStatus == BatteryStatus.Full);
        
        // 设置当前状态属性
        IsPowerCurrentlyConnected = isCurrentlyConnectedToPower;
        
        // 检测状态变化
        if (isCurrentlyConnectedToPower != wasConnectedToPower)
        {
            Debug.Log($"电源状态发生变化：从 {(wasConnectedToPower ? "已连接" : "未连接")} 变为 {(isCurrentlyConnectedToPower ? "已连接" : "未连接")}");
            
            if (isCurrentlyConnectedToPower)
            {
                // 电源已连接
                HandlePowerConnected();
            }
            else
            {
                // 电源已断开
                HandlePowerDisconnected();
            }
        }
        
        // 更新上一帧的状态记录
        wasConnectedToPower = isCurrentlyConnectedToPower;
    }
    
    /// <summary>
    /// 处理电源连接事件
    /// </summary>
    private void HandlePowerConnected()
    {
        // 设置相机禁用标志
        _shouldCameraBeDisabled = true;
        
        // 停止并释放相机资源
        ReleaseCamera();
        Debug.Log("电源已连接，相机已禁用并释放资源。");
        
        // 触发电源连接事件
        int listeners = OnPowerConnected != null ? OnPowerConnected.GetInvocationList().Length : 0;
        Debug.Log($"电源已连接，准备触发OnPowerConnected事件，当前有 {listeners} 个监听器");
        OnPowerConnected?.Invoke();
    }
    
    /// <summary>
    /// 处理电源断开事件
    /// </summary>
    private void HandlePowerDisconnected()
    {
        // 清除相机禁用标志
        _shouldCameraBeDisabled = false;
        
        Debug.Log("电源已断开，清除相机禁用标志，正在重新加载当前场景...");
        
        // 触发电源断开事件
        OnPowerDisconnected?.Invoke();
        
        // 重新加载场景
        ReloadCurrentScene();
    }
    
    /// <summary>
    /// 更新相机预览图像
    /// </summary>
    private void UpdatePreviewImage()
    {
        if (previewImage != null && webCamTexture != null && webCamTexture.isPlaying)
        {
            previewImage.texture = webCamTexture;
        }
    }
    
    /// <summary>
    /// 重新加载当前场景
    /// </summary>
    private void ReloadCurrentScene()
    {
        SceneManager.LoadScene(SceneManager.GetActiveScene().name);
    }

    private void OnDestroy()
    {
        ReleaseCamera();
    }

    private void OnApplicationQuit()
    {
        ReleaseCamera();
    }

    /// <summary>
    /// 初始化摄像头 - 参考ImageSource实现的简化版本
    /// </summary>
    /// <returns>是否成功开始初始化</returns>
    public bool InitializeCamera()
    {
        // 如果相机应该被禁用，则不初始化
        if (_shouldCameraBeDisabled)
        {
            Debug.Log("相机禁用标志已设置，不会初始化相机");
            return false;
        }
        
        if (webCamTexture != null && webCamTexture.isPlaying)
        {
            return true; // 已经初始化
        }

        ReleaseCamera(); // 释放可能存在的资源

        try
        {
            WebCamDevice[] devices = WebCamTexture.devices;
            if (devices.Length == 0)
            {
                Debug.LogError("未检测到摄像头设备!");
                return false;
            }

            // 选择摄像头设备
            string deviceName = "";
            bool deviceFound = false;

            if (!string.IsNullOrEmpty(preferredCamera))
            {
                foreach (var device in devices)
                {
                    if (device.name.Contains(preferredCamera))
                    {
                        deviceName = device.name;
                        deviceFound = true;
                        break;
                    }
                }
            }
            
            // 如果没有找到首选相机，使用第一个相机
            if (!deviceFound || string.IsNullOrEmpty(deviceName))
            {
                deviceName = devices[0].name;
            }

            webCamTexture = new WebCamTexture(deviceName, cameraWidth, cameraHeight, cameraFrameRate);
            webCamTexture.Play();
            Debug.Log($"相机实际启动分辨率: {webCamTexture.width}x{webCamTexture.height}");

            return true;
        }
        catch (Exception e)
        {
            Debug.LogError($"初始化摄像头失败: {e.Message}");
            return false;
        }
    }

    /// <summary>
    /// 释放相机资源
    /// </summary>
    public void ReleaseCamera()
    {
        if (webCamTexture != null)
        {
            if (webCamTexture.isPlaying)
            {
                webCamTexture.Stop();
            }
            
            Destroy(webCamTexture);
            webCamTexture = null;
        }
        
        // 清理预览
        if (previewImage != null && previewImage.texture == webCamTexture)
        {
            previewImage.texture = null;
        }
        
        Debug.Log("相机资源已释放");
    }

    /// <summary>
    /// 捕获当前帧并返回Texture2D
    /// </summary>
    /// <returns>当前帧的Texture2D副本</returns>
    public Texture2D CaptureFrame()
    {
        if (!IsCameraReady)
        {
            Debug.LogWarning("相机未准备好，无法捕获图像");
            return null;
        }

        if (isCapturingFrame)
        {
            Debug.LogWarning("正在捕获另一帧，请稍后重试");
            return null;
        }

        isCapturingFrame = true;

        try
        {
            int width = webCamTexture.width;
            int height = webCamTexture.height;
            
            Texture2D texture = new Texture2D(width, height, TextureFormat.RGB24, false);
            texture.SetPixels(webCamTexture.GetPixels());
            texture.Apply();
            
            Debug.Log($"已捕获帧，分辨率: {width}x{height}");
            return texture;
        }
        catch (Exception e)
        {
            Debug.LogError($"捕获图像失败: {e.Message}");
            return null;
        }
        finally
        {
            isCapturingFrame = false;
        }
    }

    /// <summary>
    /// 设置预览UI组件
    /// </summary>
    /// <param name="rawImage">要用于预览的RawImage组件</param>
    public void SetPreviewImage(RawImage rawImage)
    {
        previewImage = rawImage;
        if (previewImage != null && webCamTexture != null && webCamTexture.isPlaying)
        {
            previewImage.texture = webCamTexture;
        }
    }
} 