/**
 * TaskListItem.cs
 * 
 * 任务列表项UI组件，负责显示单个任务的详细信息（序号.<b>location</b>: content）
 * 并通过字体颜色区分激活/未激活状态。
 */

using UnityEngine;
using TMPro;
public class TaskListItem : MonoBehaviour
{
    [SerializeField] private TextMeshProUGUI taskInfoText; // 用于显示组合的任务信息 (序号.<b>location</b>: content)

    [Header("字体颜色设置")]
    [SerializeField] private Color activeColor = new Color32(255, 214, 0, 255);    // 激活任务字体颜色（黄色）
    [SerializeField] private Color inactiveColor = new Color32(248, 248, 216, 255); // 未激活任务字体颜色（米白色）

    // 任务状态
    private bool isActive = false;

    // 设置任务文本内容，地点加粗
    public void SetTaskText(string location, string content)
    {
        if (taskInfoText != null)
        {
            // 使用富文本标签<b>加粗地点
            taskInfoText.text = $"<b>{location}</b>: {content}";
        }
    }

    // 设置任务文本内容，包含序号，地点加粗
    public void SetTaskText(int number, string location, string content)
    {
        if (taskInfoText != null)
        {
            // 添加序号，使用富文本标签<b>加粗地点
            taskInfoText.text = $"{number}.<b>{location}</b>: {content}";
        }
    }

    // 设置任务文本内容，包含序号和进度，地点加粗
    public void SetTaskText(int number, string location, string content, int completedCount, int totalCount)
    {
        if (taskInfoText != null)
        {
            // 添加序号和进度信息，使用富文本标签<b>加粗地点
            if (totalCount > 1)
            {
                taskInfoText.text = $"{number}.<b>{location}</b>: {content} ({completedCount}/{totalCount})";
            }
            else
            {
                // 只有一个标签时，不显示进度
                taskInfoText.text = $"{number}.<b>{location}</b>: {content}";
            }
        }
    }

    // 设置任务为活动状态
    public void SetActive(bool active)
    {
        isActive = active;
        UpdateVisualState();
    }

    // 更新视觉样式（仅字体颜色）
    private void UpdateVisualState()
    {
        if (taskInfoText != null)
        {
            taskInfoText.color = isActive ? activeColor : inactiveColor;
        }
    }
} 