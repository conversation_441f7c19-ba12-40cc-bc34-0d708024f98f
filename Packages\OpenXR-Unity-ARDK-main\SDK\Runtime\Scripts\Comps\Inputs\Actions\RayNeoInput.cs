//------------------------------------------------------------------------------
// <auto-generated>
//     This code was auto-generated by com.unity.inputsystem:InputActionCodeGenerator
//     version 1.7.0
//     from Packages/com.unity.xr.rayneo.openxr/SDK/Runtime/Scripts/Comps/Inputs/Actions/RayNeoInput.inputactions
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine.InputSystem;
using UnityEngine.InputSystem.Utilities;

public partial class @RayNeoInput: IInputActionCollection2, IDisposable
{
    public InputActionAsset asset { get; }
    public @RayNeoInput()
    {
        asset = InputActionAsset.FromJson(@"{
    ""name"": ""RayNeoInput"",
    ""maps"": [
        {
            ""name"": ""SimpleTouch"",
            ""id"": ""3ee82658-13e9-43a1-b803-5c00561e5f49"",
            ""actions"": [
                {
                    ""name"": ""Tap"",
                    ""type"": ""Button"",
                    ""id"": ""f65f1bc0-5c7c-4d25-ae46-f4ead2976053"",
                    ""expectedControlType"": ""Button"",
                    ""processors"": """",
                    ""interactions"": ""Tap"",
                    ""initialStateCheck"": false
                },
                {
                    ""name"": ""Position"",
                    ""type"": ""PassThrough"",
                    ""id"": ""7d789459-0fa4-4d1e-b14d-6a2885d3252a"",
                    ""expectedControlType"": ""Vector2"",
                    ""processors"": """",
                    ""interactions"": """",
                    ""initialStateCheck"": false
                },
                {
                    ""name"": ""Press"",
                    ""type"": ""PassThrough"",
                    ""id"": ""46cc4698-f59d-4059-bb8e-9c201ad1ffae"",
                    ""expectedControlType"": """",
                    ""processors"": """",
                    ""interactions"": """",
                    ""initialStateCheck"": false
                }
            ],
            ""bindings"": [
                {
                    ""name"": """",
                    ""id"": ""29130e4c-b31c-4a56-b73a-096bbf7c1465"",
                    ""path"": ""<Mouse>/leftButton"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": """",
                    ""action"": ""Tap"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""a4fb838e-cafc-48b5-a432-40ac4e7124cd"",
                    ""path"": ""<Touchscreen>/Press"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": """",
                    ""action"": ""Tap"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""4537a551-5f67-4c59-9af4-39f5e22c4d32"",
                    ""path"": ""<Mouse>/position"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": """",
                    ""action"": ""Position"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""0a3cfdba-4b0a-46fe-8ae9-8af1ee59d297"",
                    ""path"": ""<Touchscreen>/position"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": """",
                    ""action"": ""Position"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""786c51c8-605f-48d5-b9d7-cf09f1a41335"",
                    ""path"": ""<Mouse>/press"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": """",
                    ""action"": ""Press"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""72efd082-c0a5-4374-b7dd-8f51c71c2cd3"",
                    ""path"": ""<Touchscreen>/Press"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": """",
                    ""action"": ""Press"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                }
            ]
        },
        {
            ""name"": ""Ring"",
            ""id"": ""982be9de-2fc3-4ab5-868b-3e53b23ec25b"",
            ""actions"": [
                {
                    ""name"": ""XRPose"",
                    ""type"": ""PassThrough"",
                    ""id"": ""33cefc00-2bd8-42c4-8414-5be7577e6822"",
                    ""expectedControlType"": ""Pose"",
                    ""processors"": """",
                    ""interactions"": """",
                    ""initialStateCheck"": true
                },
                {
                    ""name"": ""TouchPadClick"",
                    ""type"": ""Button"",
                    ""id"": ""a82eefa0-23f6-4b3c-831d-36615c5f673c"",
                    ""expectedControlType"": ""Button"",
                    ""processors"": """",
                    ""interactions"": """",
                    ""initialStateCheck"": false
                },
                {
                    ""name"": ""HeavyClick"",
                    ""type"": ""Button"",
                    ""id"": ""26ede634-498a-4303-8dbe-764200305db4"",
                    ""expectedControlType"": ""Button"",
                    ""processors"": """",
                    ""interactions"": """",
                    ""initialStateCheck"": false
                },
                {
                    ""name"": ""Rotation"",
                    ""type"": ""PassThrough"",
                    ""id"": ""6d4e4618-1ba6-4cde-958a-2be467a89265"",
                    ""expectedControlType"": ""Quaternion"",
                    ""processors"": """",
                    ""interactions"": """",
                    ""initialStateCheck"": false
                },
                {
                    ""name"": ""Position"",
                    ""type"": ""PassThrough"",
                    ""id"": ""75c5dd53-87a8-4afd-883f-102a8d83b423"",
                    ""expectedControlType"": ""Vector3"",
                    ""processors"": """",
                    ""interactions"": """",
                    ""initialStateCheck"": false
                }
            ],
            ""bindings"": [
                {
                    ""name"": """",
                    ""id"": ""89321d41-b63a-47a8-b787-a665f09e5785"",
                    ""path"": ""<RayNeoRingController>/devicePose"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": """",
                    ""action"": ""XRPose"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""74d8e7b8-419c-46dc-b6d1-7dcf9ac5386a"",
                    ""path"": ""<RingInputDevice>/button_touch"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": """",
                    ""action"": ""TouchPadClick"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""817dae67-749c-4909-9f49-d61b1b565677"",
                    ""path"": ""<RingInputDevice>/button_heavy"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": """",
                    ""action"": ""HeavyClick"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""b0bc7fbf-75a0-4244-9fb8-ac89f17a727e"",
                    ""path"": ""<RayNeoRingController>/homeButton"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": """",
                    ""action"": ""HeavyClick"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""e29aad5e-5708-4e16-8329-1999fed88b28"",
                    ""path"": ""<RingInputDevice>/devicePose"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": """",
                    ""action"": ""Rotation"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                },
                {
                    ""name"": """",
                    ""id"": ""82ad01de-e7ce-4a55-869c-2ed650c19c01"",
                    ""path"": ""<RingInputDevice>/touchPosition"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": """",
                    ""action"": ""Position"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                }
            ]
        },
        {
            ""name"": ""CellPhone"",
            ""id"": ""c19b0352-7d2b-403d-ab29-7bbfdcfc782e"",
            ""actions"": [
                {
                    ""name"": ""Pose"",
                    ""type"": ""PassThrough"",
                    ""id"": ""b148c107-5c45-444b-a1df-d9e3506ec2ef"",
                    ""expectedControlType"": ""Pose"",
                    ""processors"": """",
                    ""interactions"": """",
                    ""initialStateCheck"": false
                }
            ],
            ""bindings"": [
                {
                    ""name"": """",
                    ""id"": ""504a76bb-c72a-4156-8723-909fe8312086"",
                    ""path"": ""<RayNeoCellPhoneController>/devicePose"",
                    ""interactions"": """",
                    ""processors"": """",
                    ""groups"": """",
                    ""action"": ""Pose"",
                    ""isComposite"": false,
                    ""isPartOfComposite"": false
                }
            ]
        }
    ],
    ""controlSchemes"": []
}");
        // SimpleTouch
        m_SimpleTouch = asset.FindActionMap("SimpleTouch", throwIfNotFound: true);
        m_SimpleTouch_Tap = m_SimpleTouch.FindAction("Tap", throwIfNotFound: true);
        m_SimpleTouch_Position = m_SimpleTouch.FindAction("Position", throwIfNotFound: true);
        m_SimpleTouch_Press = m_SimpleTouch.FindAction("Press", throwIfNotFound: true);
        // Ring
        m_Ring = asset.FindActionMap("Ring", throwIfNotFound: true);
        m_Ring_XRPose = m_Ring.FindAction("XRPose", throwIfNotFound: true);
        m_Ring_TouchPadClick = m_Ring.FindAction("TouchPadClick", throwIfNotFound: true);
        m_Ring_HeavyClick = m_Ring.FindAction("HeavyClick", throwIfNotFound: true);
        m_Ring_Rotation = m_Ring.FindAction("Rotation", throwIfNotFound: true);
        m_Ring_Position = m_Ring.FindAction("Position", throwIfNotFound: true);
        // CellPhone
        m_CellPhone = asset.FindActionMap("CellPhone", throwIfNotFound: true);
        m_CellPhone_Pose = m_CellPhone.FindAction("Pose", throwIfNotFound: true);
    }

    public void Dispose()
    {
        UnityEngine.Object.Destroy(asset);
    }

    public InputBinding? bindingMask
    {
        get => asset.bindingMask;
        set => asset.bindingMask = value;
    }

    public ReadOnlyArray<InputDevice>? devices
    {
        get => asset.devices;
        set => asset.devices = value;
    }

    public ReadOnlyArray<InputControlScheme> controlSchemes => asset.controlSchemes;

    public bool Contains(InputAction action)
    {
        return asset.Contains(action);
    }

    public IEnumerator<InputAction> GetEnumerator()
    {
        return asset.GetEnumerator();
    }

    IEnumerator IEnumerable.GetEnumerator()
    {
        return GetEnumerator();
    }

    public void Enable()
    {
        asset.Enable();
    }

    public void Disable()
    {
        asset.Disable();
    }

    public IEnumerable<InputBinding> bindings => asset.bindings;

    public InputAction FindAction(string actionNameOrId, bool throwIfNotFound = false)
    {
        return asset.FindAction(actionNameOrId, throwIfNotFound);
    }

    public int FindBinding(InputBinding bindingMask, out InputAction action)
    {
        return asset.FindBinding(bindingMask, out action);
    }

    // SimpleTouch
    private readonly InputActionMap m_SimpleTouch;
    private List<ISimpleTouchActions> m_SimpleTouchActionsCallbackInterfaces = new List<ISimpleTouchActions>();
    private readonly InputAction m_SimpleTouch_Tap;
    private readonly InputAction m_SimpleTouch_Position;
    private readonly InputAction m_SimpleTouch_Press;
    public struct SimpleTouchActions
    {
        private @RayNeoInput m_Wrapper;
        public SimpleTouchActions(@RayNeoInput wrapper) { m_Wrapper = wrapper; }
        public InputAction @Tap => m_Wrapper.m_SimpleTouch_Tap;
        public InputAction @Position => m_Wrapper.m_SimpleTouch_Position;
        public InputAction @Press => m_Wrapper.m_SimpleTouch_Press;
        public InputActionMap Get() { return m_Wrapper.m_SimpleTouch; }
        public void Enable() { Get().Enable(); }
        public void Disable() { Get().Disable(); }
        public bool enabled => Get().enabled;
        public static implicit operator InputActionMap(SimpleTouchActions set) { return set.Get(); }
        public void AddCallbacks(ISimpleTouchActions instance)
        {
            if (instance == null || m_Wrapper.m_SimpleTouchActionsCallbackInterfaces.Contains(instance)) return;
            m_Wrapper.m_SimpleTouchActionsCallbackInterfaces.Add(instance);
            @Tap.started += instance.OnTap;
            @Tap.performed += instance.OnTap;
            @Tap.canceled += instance.OnTap;
            @Position.started += instance.OnPosition;
            @Position.performed += instance.OnPosition;
            @Position.canceled += instance.OnPosition;
            @Press.started += instance.OnPress;
            @Press.performed += instance.OnPress;
            @Press.canceled += instance.OnPress;
        }

        private void UnregisterCallbacks(ISimpleTouchActions instance)
        {
            @Tap.started -= instance.OnTap;
            @Tap.performed -= instance.OnTap;
            @Tap.canceled -= instance.OnTap;
            @Position.started -= instance.OnPosition;
            @Position.performed -= instance.OnPosition;
            @Position.canceled -= instance.OnPosition;
            @Press.started -= instance.OnPress;
            @Press.performed -= instance.OnPress;
            @Press.canceled -= instance.OnPress;
        }

        public void RemoveCallbacks(ISimpleTouchActions instance)
        {
            if (m_Wrapper.m_SimpleTouchActionsCallbackInterfaces.Remove(instance))
                UnregisterCallbacks(instance);
        }

        public void SetCallbacks(ISimpleTouchActions instance)
        {
            foreach (var item in m_Wrapper.m_SimpleTouchActionsCallbackInterfaces)
                UnregisterCallbacks(item);
            m_Wrapper.m_SimpleTouchActionsCallbackInterfaces.Clear();
            AddCallbacks(instance);
        }
    }
    public SimpleTouchActions @SimpleTouch => new SimpleTouchActions(this);

    // Ring
    private readonly InputActionMap m_Ring;
    private List<IRingActions> m_RingActionsCallbackInterfaces = new List<IRingActions>();
    private readonly InputAction m_Ring_XRPose;
    private readonly InputAction m_Ring_TouchPadClick;
    private readonly InputAction m_Ring_HeavyClick;
    private readonly InputAction m_Ring_Rotation;
    private readonly InputAction m_Ring_Position;
    public struct RingActions
    {
        private @RayNeoInput m_Wrapper;
        public RingActions(@RayNeoInput wrapper) { m_Wrapper = wrapper; }
        public InputAction @XRPose => m_Wrapper.m_Ring_XRPose;
        public InputAction @TouchPadClick => m_Wrapper.m_Ring_TouchPadClick;
        public InputAction @HeavyClick => m_Wrapper.m_Ring_HeavyClick;
        public InputAction @Rotation => m_Wrapper.m_Ring_Rotation;
        public InputAction @Position => m_Wrapper.m_Ring_Position;
        public InputActionMap Get() { return m_Wrapper.m_Ring; }
        public void Enable() { Get().Enable(); }
        public void Disable() { Get().Disable(); }
        public bool enabled => Get().enabled;
        public static implicit operator InputActionMap(RingActions set) { return set.Get(); }
        public void AddCallbacks(IRingActions instance)
        {
            if (instance == null || m_Wrapper.m_RingActionsCallbackInterfaces.Contains(instance)) return;
            m_Wrapper.m_RingActionsCallbackInterfaces.Add(instance);
            @XRPose.started += instance.OnXRPose;
            @XRPose.performed += instance.OnXRPose;
            @XRPose.canceled += instance.OnXRPose;
            @TouchPadClick.started += instance.OnTouchPadClick;
            @TouchPadClick.performed += instance.OnTouchPadClick;
            @TouchPadClick.canceled += instance.OnTouchPadClick;
            @HeavyClick.started += instance.OnHeavyClick;
            @HeavyClick.performed += instance.OnHeavyClick;
            @HeavyClick.canceled += instance.OnHeavyClick;
            @Rotation.started += instance.OnRotation;
            @Rotation.performed += instance.OnRotation;
            @Rotation.canceled += instance.OnRotation;
            @Position.started += instance.OnPosition;
            @Position.performed += instance.OnPosition;
            @Position.canceled += instance.OnPosition;
        }

        private void UnregisterCallbacks(IRingActions instance)
        {
            @XRPose.started -= instance.OnXRPose;
            @XRPose.performed -= instance.OnXRPose;
            @XRPose.canceled -= instance.OnXRPose;
            @TouchPadClick.started -= instance.OnTouchPadClick;
            @TouchPadClick.performed -= instance.OnTouchPadClick;
            @TouchPadClick.canceled -= instance.OnTouchPadClick;
            @HeavyClick.started -= instance.OnHeavyClick;
            @HeavyClick.performed -= instance.OnHeavyClick;
            @HeavyClick.canceled -= instance.OnHeavyClick;
            @Rotation.started -= instance.OnRotation;
            @Rotation.performed -= instance.OnRotation;
            @Rotation.canceled -= instance.OnRotation;
            @Position.started -= instance.OnPosition;
            @Position.performed -= instance.OnPosition;
            @Position.canceled -= instance.OnPosition;
        }

        public void RemoveCallbacks(IRingActions instance)
        {
            if (m_Wrapper.m_RingActionsCallbackInterfaces.Remove(instance))
                UnregisterCallbacks(instance);
        }

        public void SetCallbacks(IRingActions instance)
        {
            foreach (var item in m_Wrapper.m_RingActionsCallbackInterfaces)
                UnregisterCallbacks(item);
            m_Wrapper.m_RingActionsCallbackInterfaces.Clear();
            AddCallbacks(instance);
        }
    }
    public RingActions @Ring => new RingActions(this);

    // CellPhone
    private readonly InputActionMap m_CellPhone;
    private List<ICellPhoneActions> m_CellPhoneActionsCallbackInterfaces = new List<ICellPhoneActions>();
    private readonly InputAction m_CellPhone_Pose;
    public struct CellPhoneActions
    {
        private @RayNeoInput m_Wrapper;
        public CellPhoneActions(@RayNeoInput wrapper) { m_Wrapper = wrapper; }
        public InputAction @Pose => m_Wrapper.m_CellPhone_Pose;
        public InputActionMap Get() { return m_Wrapper.m_CellPhone; }
        public void Enable() { Get().Enable(); }
        public void Disable() { Get().Disable(); }
        public bool enabled => Get().enabled;
        public static implicit operator InputActionMap(CellPhoneActions set) { return set.Get(); }
        public void AddCallbacks(ICellPhoneActions instance)
        {
            if (instance == null || m_Wrapper.m_CellPhoneActionsCallbackInterfaces.Contains(instance)) return;
            m_Wrapper.m_CellPhoneActionsCallbackInterfaces.Add(instance);
            @Pose.started += instance.OnPose;
            @Pose.performed += instance.OnPose;
            @Pose.canceled += instance.OnPose;
        }

        private void UnregisterCallbacks(ICellPhoneActions instance)
        {
            @Pose.started -= instance.OnPose;
            @Pose.performed -= instance.OnPose;
            @Pose.canceled -= instance.OnPose;
        }

        public void RemoveCallbacks(ICellPhoneActions instance)
        {
            if (m_Wrapper.m_CellPhoneActionsCallbackInterfaces.Remove(instance))
                UnregisterCallbacks(instance);
        }

        public void SetCallbacks(ICellPhoneActions instance)
        {
            foreach (var item in m_Wrapper.m_CellPhoneActionsCallbackInterfaces)
                UnregisterCallbacks(item);
            m_Wrapper.m_CellPhoneActionsCallbackInterfaces.Clear();
            AddCallbacks(instance);
        }
    }
    public CellPhoneActions @CellPhone => new CellPhoneActions(this);
    public interface ISimpleTouchActions
    {
        void OnTap(InputAction.CallbackContext context);
        void OnPosition(InputAction.CallbackContext context);
        void OnPress(InputAction.CallbackContext context);
    }
    public interface IRingActions
    {
        void OnXRPose(InputAction.CallbackContext context);
        void OnTouchPadClick(InputAction.CallbackContext context);
        void OnHeavyClick(InputAction.CallbackContext context);
        void OnRotation(InputAction.CallbackContext context);
        void OnPosition(InputAction.CallbackContext context);
    }
    public interface ICellPhoneActions
    {
        void OnPose(InputAction.CallbackContext context);
    }
}
