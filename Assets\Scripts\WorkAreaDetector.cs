/**
 * WorkAreaDetector.cs
 * 
 * 负责检测用户是否进入特定任务的工作区域（通过识别AprilTag ID），
 * 并在进入后，持续检测Tag位姿是否与目标匹配，匹配成功后自动触发拍照，
 * 标记任务完成，并通知UI管理器。
 */
using UnityEngine;
using System.Collections;
using Klak.TestTools; 
using System.Linq;
using System.Collections.Generic; 
using System;
using AprilTag; // 为了使用 TagPose 类型

public class WorkAreaDetector : MonoBehaviour
{
    [Header("依赖组件")]
    [SerializeField] private DetectionTest tagDetector;
    [SerializeField] private ARTaskUIManager uiManager;

    [Header("检测设置")]
    // 调试提示：如果在设备上难以触发，可以临时增大以下容差值进行测试
    // 例如: positionTolerance = 0.5f; rotationTolerance = 45.0f;
    [Tooltip("位姿匹配容差：位置距离（米）")]
    [SerializeField] private float positionTolerance;
    [Tooltip("位姿匹配容差：旋转角度（度）")]
    [SerializeField] private float rotationTolerance;
    [Tooltip("进入工作区后，触发自动拍照的延迟时间（秒）")]
    [SerializeField] private float autoCaptureDelay = 0.3f; // 缩短到0.3秒，更快响应

    private Coroutine autoCaptureCoroutine = null; // 自动拍照协程引用
    private bool isInWorkArea = false; // 是否已进入某个任务的工作区
    private int currentWorkAreaTaskNumber = -1; // 当前所在工作区的任务编号
    void Update()
    {
        // 获取当前所有检测到的Tag位姿 (使用 DetectedTags 属性)
        AprilTag.TagPose[] detectedTags = tagDetector.DetectedTags;

        if (detectedTags == null || detectedTags.Length == 0) // Check array Length
        {
            // 没有检测到Tag，如果之前在工作区，则离开
            if (isInWorkArea)
            {
                ExitWorkArea();
            }
            return;
        }

        // 检查是否有Tag匹配任何未完成的任务
        ARTaskUIManager.TaskData matchedTask = FindMatchingIncompleteTask(detectedTags);

        if (matchedTask != null)
        {
            // 找到了匹配的未完成任务的工作区
            if (!isInWorkArea || currentWorkAreaTaskNumber != matchedTask.number)
            {
                // 首次进入或进入了新的工作区
                EnterWorkArea(matchedTask);
            }
            else
            {                
                // 仍然在同一个工作区，检查是否满足自动拍照条件
                 CheckAutoCaptureCondition(matchedTask, detectedTags);
            }
        }
        else
        {            
             // 当前看到的Tag都不属于任何未完成的任务，如果之前在工作区，则离开
            if (isInWorkArea)
            {
                ExitWorkArea();
            }
        }
    }

    // 查找是否有检测到的Tag匹配某个未完成任务的ID
    private ARTaskUIManager.TaskData FindMatchingIncompleteTask(AprilTag.TagPose[] detectedTags)
    {
        List<ARTaskUIManager.TaskData> tasksWithIncompleteTags = uiManager.GetTasksWithIncompleteTags();
        foreach (var task in tasksWithIncompleteTags)
        {
            if (task.tagData != null && task.tagData.Any())
            {
                // 检查是否有任何未完成的tagData与检测到的tag匹配
                foreach (var tagData in task.tagData.Where(tag => !tag.isCompleted))
                {
                    if (detectedTags.Any(detectedTag => detectedTag.ID == tagData.tagId))
                    {
                        Debug.Log($"找到匹配的未完成任务: 任务{task.number}, 标签{tagData.tagId}");
                        return task; // 找到了匹配的未完成任务
                    }
                }
            }
        }
        return null; // 没有找到匹配的未完成任务
    }

    // 进入工作区处理
    private void EnterWorkArea(ARTaskUIManager.TaskData task)
    {
        // 获取当前未完成的标签ID用于显示
        var incompleteTagIds = task.GetIncompleteTagData().Select(tag => tag.tagId).ToList();
        string tagIdList = string.Join(", ", incompleteTagIds);
        
        Debug.Log($"[多标签任务] 进入任务 {task.number} 的工作区域。未完成标签: [{tagIdList}]，进度: {task.GetCompletedTagCount()}/{task.tagData?.Count ?? 0}");
        
        // 如果是多标签任务，提供额外信息
        if (task.tagData != null && task.tagData.Count > 1)
        {
            Debug.Log($"[多标签任务] 任务 {task.number} 包含 {task.tagData.Count} 个标签，用户可按任意顺序完成");
        }
        
        isInWorkArea = true;
        currentWorkAreaTaskNumber = task.number;
        uiManager.OnArriveWorkArea(task.number);

        // 停止之前的自动拍照协程（如果存在）
        StopAutoCaptureCoroutine();
    }

    // 离开工作区处理
    private void ExitWorkArea()
    {
        // 如果自动拍照协程正在运行，不要执行离开工作区的逻辑
        if (autoCaptureCoroutine != null)
        {
            Debug.Log("正在进行自动拍照，忽略离开工作区请求");
            return;
        }
        
        Debug.Log($"离开工作区域 (之前任务: {currentWorkAreaTaskNumber})。");
        isInWorkArea = false;
        currentWorkAreaTaskNumber = -1;
        
        // 通知UI返回默认状态
        // 注意：在拍照流程中，应该由ARTaskUIManager自行处理状态转换，而不是由此方法触发
        uiManager.ReturnToDefaultOrNextTaskState();

        // 停止自动拍照协程
        StopAutoCaptureCoroutine();
    }

     // 检查是否满足自动拍照条件
    private void CheckAutoCaptureCondition(ARTaskUIManager.TaskData currentTask, AprilTag.TagPose[] detectedTags)
    {
        if (autoCaptureCoroutine != null) return; // 如果已经在准备拍照，则跳过

        // 查找当前任务中所有未完成的标签ID
        var incompleteTagIds = currentTask.GetIncompleteTagData().Select(tag => tag.tagId).ToList();
        
        // 从检测到的Tags中找到与当前任务未完成标签匹配的Tag
        AprilTag.TagPose? currentDetectedTag = null;
        ARTaskUIManager.BackendTagData targetTagData = null;
        
        foreach (var detectedTag in detectedTags)
        {
            if (incompleteTagIds.Contains(detectedTag.ID))
            {
                // 使用新方法获取检测到标签的目标数据
                targetTagData = uiManager.GetTargetTagDataForDetectedTag(detectedTag.ID);
                if (targetTagData != null)
                {
                    currentDetectedTag = detectedTag;
                    Debug.Log($"在任务 {currentTask.number} 中找到匹配的检测标签 {detectedTag.ID}，开始位姿匹配");
                    break; // 找到第一个匹配的就停止
                }
            }
        }

        if (!currentDetectedTag.HasValue || targetTagData == null)
        {
            Debug.LogWarning($"CheckAutoCaptureCondition: 在任务 {currentTask.number} 中未找到匹配的检测标签或目标数据");
            return;
        }

        // 进行位姿匹配
        bool poseMatched = IsPoseMatched(currentDetectedTag.Value, targetTagData);
        if (poseMatched)
        {
            Debug.Log($"任务 {currentTask.number} 标签 {currentDetectedTag.Value.ID} 位姿匹配成功！准备在 {autoCaptureDelay} 秒后自动拍照。");
            
            // 通知UI管理器识别成功
            uiManager.NotifyRecognitionSuccess();
            
            // 启动自动拍照协程
            autoCaptureCoroutine = StartCoroutine(AutoCaptureAfterDelay(currentTask, currentDetectedTag.Value));
        }
        else
        {
            // 位姿不匹配，计算偏移并提供引导
            Vector3 positionOffset = CalculatePositionOffset(currentDetectedTag.Value, targetTagData);
            float angleOffset = CalculateRotationOffset(currentDetectedTag.Value, targetTagData);
            
            Debug.Log($"任务 {currentTask.number} 标签 {currentDetectedTag.Value.ID} 位姿不匹配，提供引导。位置偏移: {positionOffset}, 角度偏移: {angleOffset}");
            
            // 调用UI管理器的引导功能
            uiManager.UpdateGuideDirectionByOffset(positionOffset.x, positionOffset.y, positionOffset.z, angleOffset);
        }
    }

    // 延迟后自动拍照的协程
    private IEnumerator AutoCaptureAfterDelay(ARTaskUIManager.TaskData taskToComplete, AprilTag.TagPose detectedPose)
    {
        yield return new WaitForSeconds(autoCaptureDelay);
        
        // 拍照前快速验证标签是否仍在视野中
        AprilTag.TagPose[] currentTags = tagDetector.DetectedTags;
        if (currentTags == null || currentTags.Length == 0)
        {
            Debug.LogWarning($"拍照时标签丢失，重新开始检测 (任务 {taskToComplete.number})");
            autoCaptureCoroutine = null;
            yield break;
        }
        
        // 检查目标标签是否仍在检测结果中
        var currentDetectedTag = currentTags.FirstOrDefault(t => t.ID == detectedPose.ID);
        if (currentDetectedTag.Equals(default(AprilTag.TagPose)))
        {
            Debug.LogWarning($"目标标签 {detectedPose.ID} 不再可见，重新开始检测 (任务 {taskToComplete.number})");
            autoCaptureCoroutine = null;
            yield break;
        }

        Debug.Log($"自动为任务 {taskToComplete.number} 的标签 {detectedPose.ID} 拍照。");

        // 使用WebCameraManager直接获取照片
        Texture2D photoTexture = WebCameraManager.Instance.CaptureFrame();

        if (photoTexture == null)
        {
            Debug.LogError("自动拍照失败：无法创建纹理副本。");
            StopAutoCaptureCoroutine(); // 停止协程
            yield break;
        }

        // 2. 生成文件名 (任务号_TagID_时间戳.png)
        long timestampValue = (long)(DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc)).TotalMilliseconds;
        string timestamp = timestampValue.ToString();
        string photoName = $"{taskToComplete.number}_{detectedPose.ID}_{timestamp}.png";

        // 3. 保存照片 (使用 BackendDataManager)
        bool saveSuccess = BackendDataManager.Instance.SaveTaskCompletionPhoto(photoTexture, photoName);

        if (!saveSuccess)
        {            
             Debug.LogError($"使用 BackendDataManager 保存任务完成照片失败: {photoName}");
             // 即使保存失败，也继续完成任务流程？或者需要重试？当前选择继续。
        }
        else
        {            
             Debug.Log($"任务完成照片已成功请求保存: {photoName}");
        }

        // 4. 先通知UI显示照片，然后标记标签完成（调整顺序）
        uiManager.OnPhotoTaken(photoTexture);
        
        // 等待一段时间，确保照片预览显示完毕
        yield return new WaitForSeconds(0.5f);
        
        // 然后标记标签完成（使用新的方法）
        uiManager.MarkTagAsCompleted(taskToComplete.number, detectedPose.ID);

        // 5. 离开当前工作区状态（因为标签已完成）
        // 不要在此处调用ExitWorkArea，让UI管理器自行处理状态转换
        // 现在由ARTaskUIManager的ShowTaskCompletedUI和ReturnToDefaultOrNextTaskState处理
        
        // 协程完成，重置引用
        autoCaptureCoroutine = null; 
    }

    // 停止自动拍照协程
    private void StopAutoCaptureCoroutine()
    {
        if (autoCaptureCoroutine != null)
        {
            Debug.Log("停止自动拍照协程。");
            StopCoroutine(autoCaptureCoroutine);
            autoCaptureCoroutine = null;
        }
    }

    // 比较检测到的位姿和目标位姿是否在容差范围内
    private bool IsPoseMatched(AprilTag.TagPose detectedPose, ARTaskUIManager.BackendTagData targetData)
    {
        if (targetData == null) return false;

        Vector3 targetPosition = new Vector3(targetData.posX, targetData.posY, targetData.posZ);
        Quaternion targetRotation = Quaternion.Euler(targetData.rotX, targetData.rotY, targetData.rotZ);

        float positionDistance = Vector3.Distance(detectedPose.Position, targetPosition);
        float rotationAngle = Quaternion.Angle(detectedPose.Rotation, targetRotation);

        bool positionMatched = positionDistance <= positionTolerance;
        bool rotationMatched = rotationAngle <= rotationTolerance;
        bool overallMatched = positionMatched && rotationMatched;
        
        Debug.Log($"[位姿匹配详情] 检测位置: {detectedPose.Position.ToString("F3")}, 目标位置: {targetPosition.ToString("F3")}");
        Debug.Log($"[位姿匹配详情] 检测角度: {detectedPose.Rotation.eulerAngles.ToString("F1")}, 目标角度: {targetRotation.eulerAngles.ToString("F1")}");
        Debug.Log($"[位姿匹配结果] 位置距离: {positionDistance:F3}m ({(positionMatched ? "通过" : "不通过")}, 容差: {positionTolerance:F3}m)");
        Debug.Log($"[位姿匹配结果] 角度差异: {rotationAngle:F1}° ({(rotationMatched ? "通过" : "不通过")}, 容差: {rotationTolerance:F1}°)");
        Debug.Log($"[位姿匹配结果] 最终结果: {(overallMatched ? "匹配成功" : "匹配失败")}");

        return overallMatched;
    }

    // 计算位置偏移，考虑相机视角
    private Vector3 CalculatePositionOffset(AprilTag.TagPose detectedPose, ARTaskUIManager.BackendTagData targetData)
    {
        Vector3 targetPosition = new Vector3(targetData.posX, targetData.posY, targetData.posZ);
        Vector3 worldOffset = detectedPose.Position - targetPosition;
        
        // 获取相机的前进方向和右方向（假设标签检测器使用主相机）
        Transform cameraTransform = Camera.main.transform;
        
        // 将世界偏移转换为相对于相机的偏移
        Vector3 cameraForward = Vector3.ProjectOnPlane(cameraTransform.forward, Vector3.up).normalized;
        Vector3 cameraRight = Vector3.Cross(Vector3.up, cameraForward).normalized;
        
        // 计算相对于相机的X偏移（左右）和Z偏移（前后）
        float offsetX = Vector3.Dot(worldOffset, cameraRight); 
        float offsetZ = Vector3.Dot(worldOffset, cameraForward);
        
        // Y轴偏移（上下）保持不变
        float offsetY = worldOffset.y;
        
        Vector3 relativeOffset = new Vector3(offsetX, offsetY, offsetZ);
        
        Debug.Log($"位置偏移: 世界坐标={worldOffset}, 相对坐标={relativeOffset}");
        
        return relativeOffset;
    }

    // 计算旋转偏移（返回带方向的偏移角度，正值表示需要向右转，负值表示需要向左转）
    private float CalculateRotationOffset(AprilTag.TagPose detectedPose, ARTaskUIManager.BackendTagData targetData)
    {
        Quaternion targetRotation = Quaternion.Euler(targetData.rotX, targetData.rotY, targetData.rotZ);
        
        // 仅考虑Y轴（水平方向）的旋转
        float targetAngleY = targetRotation.eulerAngles.y;
        float detectedAngleY = detectedPose.Rotation.eulerAngles.y;
        
        // 标准化角度到[-180, 180]范围内
        targetAngleY = NormalizeAngle(targetAngleY);
        detectedAngleY = NormalizeAngle(detectedAngleY);
        
        // 计算Y轴旋转差值
        float angleOffset = targetAngleY - detectedAngleY;
        
        // 标准化结果到[-180, 180]范围内
        angleOffset = NormalizeAngle(angleOffset);
        
        Debug.Log($"旋转偏移: 目标角度={targetAngleY:F1}, 检测角度={detectedAngleY:F1}, 偏移={angleOffset:F1}");
        
        return angleOffset;
    }
    
    // 将角度标准化到[-180, 180]范围内
    private float NormalizeAngle(float angle)
    {
        angle = angle % 360;
        if (angle > 180)
            angle -= 360;
        else if (angle < -180)
            angle += 360;
        return angle;
    }
    
    /// <summary>
    /// 获取位置匹配容差值（米）
    /// </summary>
    /// <returns>位置容差值，单位：米</returns>
    public float GetPositionTolerance()
    {
        return positionTolerance;
    }
    
    /// <summary>
    /// 获取角度匹配容差值（度）
    /// </summary>
    /// <returns>角度容差值，单位：度</returns>
    public float GetRotationTolerance()
    {
        return rotationTolerance;
    }
}  