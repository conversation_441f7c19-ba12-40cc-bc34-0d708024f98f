/**
 * TemplatePhotoManager.cs
 * 
 * 专门负责管理第一次手动拍摄照片的脚本。
 * 包括保存模板照片和管理相关存储路径。
 * 使用单例模式提供全局访问点。
 */
using UnityEngine;
using System.IO;
using System.Collections.Generic;

public class TemplatePhotoManager : MonoBehaviour
{
    // --- Singleton Pattern ---
    private static TemplatePhotoManager _instance;
    public static TemplatePhotoManager Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = FindObjectOfType<TemplatePhotoManager>();
                if (_instance == null)
                {
                    GameObject singletonObject = new GameObject("TemplatePhotoManager");
                    _instance = singletonObject.AddComponent<TemplatePhotoManager>();
                    DontDestroyOnLoad(singletonObject); 
                    Debug.Log("TemplatePhotoManager instance created.");
                }
            }
            return _instance;
        }
    }

    // --- Paths ---
    [Header("存储路径设置")]
    [SerializeField] private string templatePhotoFolderName = "tagImg";

    private string templatePhotoPath;

    // --- Initialization ---
    private void Awake()
    {
        if (_instance != null && _instance != this)
        {
            Destroy(gameObject);
            return;
        }
        _instance = this;
        DontDestroyOnLoad(gameObject);

        InitializePaths();
    }

    private void InitializePaths()
    {
        templatePhotoPath = Path.Combine(Application.persistentDataPath, templatePhotoFolderName);

        // 创建目录（如果不存在）
        if (!Directory.Exists(templatePhotoPath))
        {
            Directory.CreateDirectory(templatePhotoPath);
            Debug.Log($"Created directory: {templatePhotoPath}");
        }
    }

    // --- Public Methods ---

    /// <summary>
    /// 保存第一次手动拍摄的模板照片。
    /// </summary>
    /// <param name="photoTexture">照片纹理</param>
    /// <param name="fileName">要保存的文件名 (包含扩展名, 如 TagID=5_PosX=....png)</param>
    /// <returns>如果保存成功返回true，否则返回false</returns>
    public bool SaveTemplatePhoto(Texture2D photoTexture, string fileName)
    {
        if (photoTexture == null || string.IsNullOrEmpty(fileName))
        {
            Debug.LogError("SaveTemplatePhoto: 照片纹理或文件名为空。");
            return false;
        }

        try
        {
            byte[] bytes = photoTexture.EncodeToPNG();
            string fullPath = Path.Combine(templatePhotoPath, fileName);
            File.WriteAllBytes(fullPath, bytes);
            Debug.Log($"模板照片已保存至: {fullPath}");
            return true;
        }
        catch (System.Exception e)
        {
            Debug.LogError($"保存模板照片失败: {e.Message}");
            return false;
        }
    }
} 