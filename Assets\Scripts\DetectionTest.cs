using UnityEngine;
using System.Linq;
using UI = UnityEngine.UI;
using System.Collections.Generic;
using System.IO;
using System.Text.RegularExpressions;
using System.Globalization;
using RayNeo; // 添加RayNeo命名空间引用以访问SimpleTouch

// 修改为public以便其他脚本可以访问
public class DetectionTest : MonoBehaviour
{
    [SerializeField] int _decimation = 4;
    [SerializeField] float _tagSize = 0.05f;
    [SerializeField] Material _tagMaterial = null;
    [SerializeField] UI.RawImage _webcamPreview = null;
    [SerializeField] UI.Text _debugText = null;

    [Header("Pose Matching Settings")]
    [SerializeField] float _maxPositionDifference = 0.1f;
    [SerializeField] float _maxAngleDifference = 15.0f;

    private struct StoredTagData
    {
        public int TagID;
        public Vector3 Position;
        public Quaternion Rotation; 
        public string Source;  // 来源标识（现在用于区分是哪个任务的数据）
    }
    private List<StoredTagData> _storedTagDatabase = new List<StoredTagData>();

    AprilTag.TagDetector _detector;
    
    // 添加WebCameraManager引用
    private WebCameraManager _cameraManager;

    // 添加公开属性，替代DetectionTestExtension的功能
    public AprilTag.TagPose[] DetectedTags
    {
        get
        {
            return _detector?.DetectedTags.ToArray() ?? new AprilTag.TagPose[0];
        }
    }
    
    // 添加与ImageSource兼容的Texture属性
    public Texture Texture
    {
        get
        {
            return _cameraManager?.WebCamTexture;
        }
    }

    void Start()
    {
        // 获取WebCameraManager实例
        _cameraManager = WebCameraManager.Instance;
        
        // 初始化相机
        _cameraManager.InitializeCamera();
        
        // 设置相机预览
        if (_webcamPreview != null)
        {
            _cameraManager.SetPreviewImage(_webcamPreview);
        }
        
        // 初始化AprilTag检测器
        Vector2Int dims = _cameraManager.GetCameraResolution();
        _detector = new AprilTag.TagDetector(dims.x, dims.y, _decimation);
        
        // 注册SimpleTouch事件（双击退出功能）
        if (SimpleTouch.Instance != null)
        {
            SimpleTouch.Instance.OnDoubleTap.AddListener(HandleDoubleTapExit);
            Debug.Log("已注册双击退出事件监听");
        }
        else
        {
            Debug.LogWarning("SimpleTouch实例未找到，双击退出功能可能无法使用");
        }
        
        // 尝试立即加载一次
        LoadTagsFromBackendData();
    }

    // 使用BackendDataManager加载标签数据
    void LoadTagsFromBackendData()
    {
        _storedTagDatabase.Clear();
        
        // 获取所有任务数据
        var allTasks = BackendDataManager.Instance.LoadAndParseTaskData();
        
        // 从任务数据中提取标签信息
        foreach (var task in allTasks) 
        {
            if (task.tagData != null && task.tagData.Count > 0)
            {
                foreach (var tagData in task.tagData)
                {
                    StoredTagData data = new StoredTagData();
                    data.TagID = tagData.tagId;
                    data.Position = new Vector3(tagData.posX, tagData.posY, tagData.posZ);
                    data.Rotation = Quaternion.Euler(tagData.rotX, tagData.rotY, tagData.rotZ);
                    data.Source = $"Task_{task.number}";
                    
                    _storedTagDatabase.Add(data);
                    Debug.Log($"从任务 {task.number} 加载标签数据: ID={tagData.tagId}, Pos={data.Position}, Rot(Euler)=({tagData.rotX},{tagData.rotY},{tagData.rotZ})");
                }
            }
        }
        
        Debug.Log($"总共从JSON任务数据中加载了 {_storedTagDatabase.Count} 个标签条目。");
    }

    void OnDestroy()
    {
        _detector.Dispose();
        
        // 移除SimpleTouch事件监听
        if (SimpleTouch.Instance != null)
        {
            SimpleTouch.Instance.OnDoubleTap.RemoveListener(HandleDoubleTapExit);
        }
    }

    void LateUpdate()
    {
        // 确保相机已初始化
        if (_cameraManager == null || !_cameraManager.IsCameraReady || _detector == null)
        {
            return;
        }
        
        // 获取WebCamTexture
        var webCamTexture = _cameraManager.WebCamTexture;
        if (webCamTexture == null)
        {
            return;
        }
        
        // 更新预览纹理
        if (_webcamPreview != null && webCamTexture.isPlaying)
        {
            _webcamPreview.texture = webCamTexture;
        }
        
        // 获取图像数据并处理
        var image = webCamTexture.AsSpan();
        if (image.IsEmpty) return;

        var fov = Camera.main.fieldOfView * Mathf.Deg2Rad;
        _detector.ProcessImage(image, fov, _tagSize);

        foreach (var tag in _detector.DetectedTags)
        {
            if (_storedTagDatabase.Any())
            {
                MatchTagWithStoredData(tag);
            }
        }

        if (Time.frameCount % 30 == 0)
            _debugText.text = _detector.ProfileData.Aggregate
              ("Profile (usec)", (c, n) => $"{c}\n{n.name} : {n.time}");
    }

    public List<int> GetDetectedTagIDs()
    {
        List<int> tagIDs = new List<int>();
        var tags = DetectedTags;
        
        if (tags != null)
        {
            foreach (var tag in tags)
            {
                tagIDs.Add(tag.ID);
            }
        }
        
        return tagIDs;
    }
    
    public bool IsTagDetected(int tagID)
    {
        var tags = DetectedTags;
        
        if (tags != null)
        {
            foreach (var tag in tags)
            {
                if (tag.ID == tagID)
                {
                    return true;
                }
            }
        }
        
        return false;
    }

    // 匹配实时检测的标签与存储的JSON标签数据
    void MatchTagWithStoredData(AprilTag.TagPose liveTag)
    {
        Debug.Log($"尝试匹配实时标签ID: {liveTag.ID} 与JSON数据（已加载 {_storedTagDatabase.Count} 项）...");
        bool matchFoundInDatabase = false;
        
        foreach (var storedData in _storedTagDatabase)
        {
            Debug.Log($"  检查存储的标签ID: {storedData.TagID} 来自 '{storedData.Source}' 对比实时标签ID: {liveTag.ID}");
            
            if (liveTag.ID == storedData.TagID)
            {
                float positionDifference = Vector3.Distance(liveTag.Position, storedData.Position);
                
                if (positionDifference <= _maxPositionDifference)
                { 
                    float angleDifference = Quaternion.Angle(liveTag.Rotation, storedData.Rotation);
                    
                    if (angleDifference <= _maxAngleDifference)
                    {
                        Debug.Log($"匹配成功! 实时标签ID: {liveTag.ID} 匹配到来源: '{storedData.Source}' (存储标签ID: {storedData.TagID}). 位置差异: {positionDifference:F3}, 角度差异: {angleDifference:F2}");
                        matchFoundInDatabase = true;
                        return;
                    }
                    else
                    {
                        Debug.LogWarning($"旋转不匹配 - 实时标签ID: {liveTag.ID} 与来源: '{storedData.Source}'. 角度差异: {angleDifference:F2} > 阈值: {_maxAngleDifference}");
                    }
                }
                else
                {
                    Debug.LogWarning($"位置不匹配 - 实时标签ID: {liveTag.ID} 与来源: '{storedData.Source}'. 位置差异: {positionDifference:F3} > 阈值: {_maxPositionDifference}");
                }
            }
        }
        
        if (!matchFoundInDatabase)
        {
            Debug.Log($"在JSON数据库中没有找到与实时标签ID: {liveTag.ID} 的匹配项，已检查全部 {_storedTagDatabase.Count} 个条目。");
        }
    }

    /// <summary>
    /// 允许外部脚本重新加载标签数据
    /// </summary>
    public void ReloadTagData()
    {
        LoadTagsFromBackendData();
    }
    
    // 处理双击退出
    private void HandleDoubleTapExit()
    {
        #if UNITY_EDITOR
        UnityEditor.EditorApplication.isPlaying = false;
        #else
        Application.Quit();
        #endif
    }
}
