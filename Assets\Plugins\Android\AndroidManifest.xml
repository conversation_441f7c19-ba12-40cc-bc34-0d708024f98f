<?xml version="1.0" encoding="utf-8"?>
<manifest
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.unity3d.player">

	<application 
		android:requestLegacyExternalStorage="true"
		android:label="@string/app_name"
		tools:replace="android:label">

		<activity android:name="com.rayneo.openxradapter.UnityOpenXrActivity"
                  android:theme="@style/UnityThemeSelector">
			<intent-filter>
				<action android:name="android.intent.action.MAIN" />
				<category android:name="android.intent.category.LAUNCHER" />
				<category android:name="android.intent.category.HOME" />
				<category android:name="android.intent.category.DEFAULT" />
			</intent-filter>
			<meta-data android:name="unityplayer.UnityActivity" android:value="true" />
		</activity>
		<meta-data android:name="com.rayneo.mercury.app" android:value="true" />
	</application>
	<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
	<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
	<uses-permission android:name="android.permission.WRITE_SETTINGS"/>

	<uses-permission android:name="android.permission.CAMERA"/>
	<uses-permission android:name="android.permission.INTERNET" />
	<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
	<uses-permission android:name="android.permission.CHANGE_NETWORK_STATE"/>
	<uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
	<uses-permission android:name="android.permission.CHANGE_WIFI_STATE"/>

	<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
	<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
	
	<uses-permission android:name="android.permission.SHUTDOWN" />
	<uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" />
</manifest>
